"""System information router"""

from fastapi import APIRouter
import psutil
import platform

router = APIRouter()

@router.get("/status")
async def get_system_status():
    return {
        "cpu_percent": psutil.cpu_percent(),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_usage": psutil.disk_usage('/').percent,
        "platform": platform.system(),
        "python_version": platform.python_version()
    }

@router.get("/info")
async def get_system_info():
    return {
        "platform": platform.platform(),
        "processor": platform.processor(),
        "architecture": platform.architecture(),
        "hostname": platform.node()
    }
