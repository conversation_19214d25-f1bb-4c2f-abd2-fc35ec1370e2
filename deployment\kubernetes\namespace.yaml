apiVersion: v1
kind: Namespace
metadata:
  name: jarvis
  labels:
    name: jarvis
    app: jarvis-ai-assistant
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: jarvis-config
  namespace: jarvis
data:
  DATABASE_URL: "*************************************************/jarvis_db"
  REDIS_URL: "redis://redis:6379/0"
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
---
apiVersion: v1
kind: Secret
metadata:
  name: jarvis-secrets
  namespace: jarvis
type: Opaque
data:
  SECRET_KEY: eW91ci11bHRyYS1zZWN1cmUtc2VjcmV0LWtleS1oZXJl  # base64 encoded
  OPENAI_API_KEY: ""  # Add your base64 encoded API key
  ANTHROPIC_API_KEY: ""  # Add your base64 encoded API key
  GOOGLE_API_KEY: ""  # Add your base64 encoded API key
  POSTGRES_PASSWORD: amFydmlzX3Bhc3N3b3Jk  # base64 encoded
  REDIS_PASSWORD: cmVkaXNfcGFzc3dvcmQ=  # base64 encoded
