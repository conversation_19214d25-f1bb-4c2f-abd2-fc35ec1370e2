"""
Advanced Memory and Preferences Service for JARVIS
Learns and remembers user preferences, habits, and patterns
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import numpy as np
from collections import defaultdict
from loguru import logger

from ..core.config import settings
from ..core.redis_client import redis_client

class MemoryService:
    """Advanced memory and learning service for user preferences"""
    
    def __init__(self):
        self.user_profiles = {}
        self.learning_models = {}
        self.preference_weights = {
            "frequency": 0.4,
            "recency": 0.3,
            "explicit": 0.3
        }
        
    async def initialize(self):
        """Initialize memory service"""
        logger.info("Memory service initialized")
        
    async def learn_user_preference(self, user_id: str, category: str, item: str, 
                                  action: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Learn from user interactions and preferences"""
        try:
            # Get current preferences
            preferences = await self.get_user_preferences(user_id)
            
            # Update preference based on action
            if category not in preferences:
                preferences[category] = {}
            
            if item not in preferences[category]:
                preferences[category][item] = {
                    "score": 0.0,
                    "frequency": 0,
                    "last_interaction": None,
                    "contexts": [],
                    "explicit_rating": None
                }
            
            # Update based on action type
            if action == "like" or action == "select" or action == "use":
                preferences[category][item]["score"] += 1.0
                preferences[category][item]["frequency"] += 1
            elif action == "dislike" or action == "reject":
                preferences[category][item]["score"] -= 0.5
            elif action == "love":
                preferences[category][item]["score"] += 2.0
                preferences[category][item]["frequency"] += 1
            elif action == "hate":
                preferences[category][item]["score"] -= 2.0
            
            # Update metadata
            preferences[category][item]["last_interaction"] = datetime.utcnow().isoformat()
            
            if context:
                preferences[category][item]["contexts"].append({
                    "context": context,
                    "timestamp": datetime.utcnow().isoformat(),
                    "action": action
                })
                
                # Keep only recent contexts
                if len(preferences[category][item]["contexts"]) > 10:
                    preferences[category][item]["contexts"] = preferences[category][item]["contexts"][-10:]
            
            # Store updated preferences
            await self.store_user_preferences(user_id, preferences)
            
            # Learn patterns
            await self.analyze_patterns(user_id, category, item, action, context)
            
            return {
                "message": "Preference learned",
                "category": category,
                "item": item,
                "new_score": preferences[category][item]["score"]
            }
            
        except Exception as e:
            logger.error(f"Learn preference error: {e}")
            return {"error": "Failed to learn preference"}
    
    async def get_recommendations(self, user_id: str, category: str, 
                                context: Dict[str, Any] = None, limit: int = 5) -> List[Dict[str, Any]]:
        """Get personalized recommendations based on learned preferences"""
        try:
            preferences = await self.get_user_preferences(user_id)
            
            if category not in preferences:
                return []
            
            # Calculate recommendation scores
            recommendations = []
            
            for item, data in preferences[category].items():
                score = self.calculate_recommendation_score(data, context)
                
                recommendations.append({
                    "item": item,
                    "score": score,
                    "frequency": data["frequency"],
                    "last_used": data["last_interaction"],
                    "confidence": min(1.0, data["frequency"] / 10.0)  # Confidence based on frequency
                })
            
            # Sort by score and return top recommendations
            recommendations.sort(key=lambda x: x["score"], reverse=True)
            
            return recommendations[:limit]
            
        except Exception as e:
            logger.error(f"Get recommendations error: {e}")
            return []
    
    async def predict_user_intent(self, user_id: str, current_context: Dict[str, Any]) -> Dict[str, Any]:
        """Predict user intent based on patterns and context"""
        try:
            # Get user patterns
            patterns = await self.get_user_patterns(user_id)
            
            # Analyze current context
            time_of_day = datetime.now().hour
            day_of_week = datetime.now().weekday()
            location = current_context.get("location", "unknown")
            
            # Find matching patterns
            matching_patterns = []
            
            for pattern in patterns:
                pattern_match_score = 0.0
                
                # Time similarity
                if abs(pattern.get("time_of_day", 12) - time_of_day) <= 2:
                    pattern_match_score += 0.3
                
                # Day similarity
                if pattern.get("day_of_week") == day_of_week:
                    pattern_match_score += 0.2
                
                # Location similarity
                if pattern.get("location") == location:
                    pattern_match_score += 0.3
                
                # Context similarity
                context_similarity = self.calculate_context_similarity(
                    pattern.get("context", {}), current_context
                )
                pattern_match_score += context_similarity * 0.2
                
                if pattern_match_score > 0.5:  # Threshold for pattern match
                    matching_patterns.append({
                        "pattern": pattern,
                        "match_score": pattern_match_score
                    })
            
            # Sort by match score
            matching_patterns.sort(key=lambda x: x["match_score"], reverse=True)
            
            # Generate predictions
            predictions = []
            for match in matching_patterns[:3]:  # Top 3 patterns
                pattern = match["pattern"]
                predictions.append({
                    "intent": pattern.get("intent"),
                    "action": pattern.get("action"),
                    "confidence": match["match_score"],
                    "suggested_response": pattern.get("response"),
                    "context": pattern.get("context")
                })
            
            return {
                "predictions": predictions,
                "context_analyzed": current_context,
                "patterns_matched": len(matching_patterns)
            }
            
        except Exception as e:
            logger.error(f"Intent prediction error: {e}")
            return {"error": "Failed to predict intent"}
    
    async def remember_conversation(self, user_id: str, conversation: Dict[str, Any]) -> Dict[str, Any]:
        """Remember important parts of conversations"""
        try:
            # Extract key information from conversation
            key_info = await self.extract_key_information(conversation)
            
            # Store in long-term memory
            memory_entry = {
                "type": "conversation",
                "timestamp": datetime.utcnow().isoformat(),
                "key_info": key_info,
                "full_conversation": conversation,
                "importance_score": self.calculate_importance_score(key_info)
            }
            
            # Store in Redis with appropriate TTL based on importance
            ttl = int(memory_entry["importance_score"] * 86400 * 30)  # Up to 30 days
            await redis_client.lpush(f"memories:{user_id}", memory_entry)
            await redis_client.expire(f"memories:{user_id}", ttl)
            
            # Update user profile with extracted information
            await self.update_user_profile(user_id, key_info)
            
            return {
                "message": "Conversation remembered",
                "importance_score": memory_entry["importance_score"],
                "key_info_extracted": len(key_info)
            }
            
        except Exception as e:
            logger.error(f"Remember conversation error: {e}")
            return {"error": "Failed to remember conversation"}
    
    async def recall_memory(self, user_id: str, query: str, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Recall relevant memories based on query"""
        try:
            # Get all memories for user
            memories = await redis_client.lrange(f"memories:{user_id}", 0, -1)
            
            relevant_memories = []
            
            for memory in memories:
                if isinstance(memory, dict):
                    # Calculate relevance score
                    relevance = self.calculate_memory_relevance(memory, query, context)
                    
                    if relevance > 0.3:  # Relevance threshold
                        relevant_memories.append({
                            "memory": memory,
                            "relevance": relevance,
                            "timestamp": memory.get("timestamp"),
                            "type": memory.get("type")
                        })
            
            # Sort by relevance
            relevant_memories.sort(key=lambda x: x["relevance"], reverse=True)
            
            return relevant_memories[:10]  # Return top 10 relevant memories
            
        except Exception as e:
            logger.error(f"Recall memory error: {e}")
            return []
    
    async def learn_routine(self, user_id: str, activity: str, timestamp: datetime, 
                          context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Learn user routines and habits"""
        try:
            # Get existing routines
            routines = await redis_client.get(f"routines:{user_id}", {})
            
            if activity not in routines:
                routines[activity] = {
                    "occurrences": [],
                    "patterns": {},
                    "confidence": 0.0
                }
            
            # Add new occurrence
            occurrence = {
                "timestamp": timestamp.isoformat(),
                "hour": timestamp.hour,
                "day_of_week": timestamp.weekday(),
                "context": context or {}
            }
            
            routines[activity]["occurrences"].append(occurrence)
            
            # Keep only recent occurrences (last 30 days)
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            routines[activity]["occurrences"] = [
                occ for occ in routines[activity]["occurrences"]
                if datetime.fromisoformat(occ["timestamp"]) > cutoff_date
            ]
            
            # Analyze patterns
            patterns = self.analyze_routine_patterns(routines[activity]["occurrences"])
            routines[activity]["patterns"] = patterns
            routines[activity]["confidence"] = self.calculate_routine_confidence(patterns)
            
            # Store updated routines
            await redis_client.set(f"routines:{user_id}", routines)
            
            return {
                "message": "Routine learned",
                "activity": activity,
                "confidence": routines[activity]["confidence"],
                "patterns": patterns
            }
            
        except Exception as e:
            logger.error(f"Learn routine error: {e}")
            return {"error": "Failed to learn routine"}
    
    async def predict_next_action(self, user_id: str, current_time: datetime = None) -> Dict[str, Any]:
        """Predict user's next likely action based on routines"""
        try:
            if not current_time:
                current_time = datetime.utcnow()
            
            # Get user routines
            routines = await redis_client.get(f"routines:{user_id}", {})
            
            predictions = []
            
            for activity, routine_data in routines.items():
                patterns = routine_data.get("patterns", {})
                confidence = routine_data.get("confidence", 0.0)
                
                # Check if current time matches any patterns
                current_hour = current_time.hour
                current_day = current_time.weekday()
                
                # Time-based prediction
                if "hourly_distribution" in patterns:
                    hour_probability = patterns["hourly_distribution"].get(str(current_hour), 0.0)
                    
                    if hour_probability > 0.1:  # Threshold for prediction
                        predictions.append({
                            "activity": activity,
                            "probability": hour_probability * confidence,
                            "type": "time_based",
                            "confidence": confidence
                        })
                
                # Day-based prediction
                if "daily_distribution" in patterns:
                    day_probability = patterns["daily_distribution"].get(str(current_day), 0.0)
                    
                    if day_probability > 0.1:
                        predictions.append({
                            "activity": activity,
                            "probability": day_probability * confidence,
                            "type": "day_based",
                            "confidence": confidence
                        })
            
            # Sort by probability
            predictions.sort(key=lambda x: x["probability"], reverse=True)
            
            return {
                "predictions": predictions[:5],  # Top 5 predictions
                "current_time": current_time.isoformat(),
                "total_activities_analyzed": len(routines)
            }
            
        except Exception as e:
            logger.error(f"Predict next action error: {e}")
            return {"error": "Failed to predict next action"}
    
    def calculate_recommendation_score(self, preference_data: Dict[str, Any], 
                                     context: Dict[str, Any] = None) -> float:
        """Calculate recommendation score for an item"""
        score = preference_data.get("score", 0.0)
        frequency = preference_data.get("frequency", 0)
        last_interaction = preference_data.get("last_interaction")
        
        # Base score from user preference
        recommendation_score = score * self.preference_weights["frequency"]
        
        # Frequency bonus
        frequency_score = min(1.0, frequency / 10.0)  # Normalize to 0-1
        recommendation_score += frequency_score * self.preference_weights["frequency"]
        
        # Recency bonus
        if last_interaction:
            days_since = (datetime.utcnow() - datetime.fromisoformat(last_interaction)).days
            recency_score = max(0.0, 1.0 - (days_since / 30.0))  # Decay over 30 days
            recommendation_score += recency_score * self.preference_weights["recency"]
        
        # Context relevance
        if context and preference_data.get("contexts"):
            context_relevance = self.calculate_context_relevance(
                preference_data["contexts"], context
            )
            recommendation_score += context_relevance * 0.2
        
        return recommendation_score
    
    def calculate_context_similarity(self, context1: Dict[str, Any], context2: Dict[str, Any]) -> float:
        """Calculate similarity between two contexts"""
        if not context1 or not context2:
            return 0.0
        
        common_keys = set(context1.keys()) & set(context2.keys())
        if not common_keys:
            return 0.0
        
        similarity = 0.0
        for key in common_keys:
            if context1[key] == context2[key]:
                similarity += 1.0
        
        return similarity / len(common_keys)
    
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get user preferences from storage"""
        preferences = await redis_client.get(f"preferences:{user_id}")
        return preferences or {}
    
    async def store_user_preferences(self, user_id: str, preferences: Dict[str, Any]):
        """Store user preferences"""
        await redis_client.set(f"preferences:{user_id}", preferences)
    
    async def cleanup(self):
        """Cleanup memory service"""
        logger.info("Memory service cleanup completed")
