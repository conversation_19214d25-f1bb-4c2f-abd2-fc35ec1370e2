"""
Advanced Document Generation Service for JARVIS
Ultra-advanced document creation with AI-powered content generation
"""

import asyncio
import json
import os
import tempfile
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import base64
from io import BytesIO

# Document generation imports
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from pptx import Presentation
from pptx.util import Inches as PptxInches, Pt as PptxPt
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from fpdf import FPDF
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image as PILImage, ImageDraw, ImageFont
import pandas as pd

from loguru import logger
from ..core.config import settings
from ..core.redis_client import redis_client

class DocumentGenerationService:
    """Ultra-advanced document generation with AI integration"""
    
    def __init__(self):
        self.templates = {}
        self.ai_content_generator = None
        self.chart_generator = None
        
    async def initialize(self):
        """Initialize document generation service"""
        try:
            logger.info("Initializing Document Generation Service...")
            
            # Load document templates
            await self.load_templates()
            
            # Initialize AI content generator
            await self.initialize_ai_generator()
            
            logger.info("📄 Document Generation Service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Document Generation Service: {e}")
    
    async def generate_document_intelligent(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Generate documents with AI-powered content creation"""
        try:
            doc_type = request.get("type", "word")
            content_request = request.get("content", {})
            template_name = request.get("template", "default")
            user_id = request.get("user_id")
            
            generation_result = {
                "timestamp": datetime.utcnow().isoformat(),
                "document_type": doc_type,
                "template": template_name,
                "status": "generating"
            }
            
            # Generate AI content if requested
            if content_request.get("ai_generate"):
                ai_content = await self.generate_ai_content(content_request)
                content_request.update(ai_content)
            
            # Generate document based on type
            if doc_type == "word":
                document_data = await self.generate_word_document(content_request, template_name)
            elif doc_type == "powerpoint":
                document_data = await self.generate_powerpoint_presentation(content_request, template_name)
            elif doc_type == "pdf":
                document_data = await self.generate_pdf_document(content_request, template_name)
            elif doc_type == "excel":
                document_data = await self.generate_excel_spreadsheet(content_request, template_name)
            elif doc_type == "infographic":
                document_data = await self.generate_infographic(content_request, template_name)
            else:
                raise ValueError(f"Unsupported document type: {doc_type}")
            
            generation_result.update(document_data)
            generation_result["status"] = "completed"
            
            # Store generation history
            if user_id:
                await self.store_generation_history(user_id, generation_result)
            
            return generation_result
            
        except Exception as e:
            logger.error(f"Document generation error: {e}")
            return {"error": str(e), "status": "failed"}
    
    async def generate_word_document(self, content: Dict[str, Any], template: str = "default") -> Dict[str, Any]:
        """Generate Word document with advanced formatting"""
        try:
            # Create new document
            doc = Document()
            
            # Set document properties
            doc.core_properties.title = content.get("title", "Generated Document")
            doc.core_properties.author = content.get("author", "JARVIS AI")
            doc.core_properties.subject = content.get("subject", "AI Generated Content")
            
            # Add title
            title = doc.add_heading(content.get("title", "Document Title"), 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add subtitle if provided
            if content.get("subtitle"):
                subtitle = doc.add_heading(content["subtitle"], level=1)
                subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add table of contents if requested
            if content.get("include_toc"):
                doc.add_heading("Table of Contents", level=1)
                toc_paragraph = doc.add_paragraph()
                # TOC would be generated based on headings
            
            # Add sections
            sections = content.get("sections", [])
            for section in sections:
                # Add section heading
                if section.get("heading"):
                    doc.add_heading(section["heading"], level=section.get("level", 2))
                
                # Add section content
                if section.get("content"):
                    if isinstance(section["content"], list):
                        for paragraph in section["content"]:
                            doc.add_paragraph(paragraph)
                    else:
                        doc.add_paragraph(section["content"])
                
                # Add images if provided
                if section.get("images"):
                    for image_info in section["images"]:
                        if image_info.get("path"):
                            doc.add_picture(image_info["path"], width=Inches(image_info.get("width", 6)))
                
                # Add tables if provided
                if section.get("tables"):
                    for table_data in section["tables"]:
                        await self.add_table_to_word(doc, table_data)
                
                # Add charts if provided
                if section.get("charts"):
                    for chart_data in section["charts"]:
                        chart_image = await self.generate_chart(chart_data)
                        if chart_image:
                            doc.add_picture(chart_image, width=Inches(6))
            
            # Add footer
            if content.get("footer"):
                footer = doc.sections[0].footer
                footer_paragraph = footer.paragraphs[0]
                footer_paragraph.text = content["footer"]
            
            # Save document
            doc_path = tempfile.mktemp(suffix=".docx")
            doc.save(doc_path)
            
            # Convert to bytes
            with open(doc_path, "rb") as f:
                doc_bytes = f.read()
            
            # Cleanup
            os.unlink(doc_path)
            
            return {
                "document_bytes": base64.b64encode(doc_bytes).decode(),
                "filename": f"{content.get('title', 'document')}.docx",
                "size": len(doc_bytes),
                "pages": len(doc.sections)
            }
            
        except Exception as e:
            logger.error(f"Word document generation error: {e}")
            return {"error": str(e)}
    
    async def generate_powerpoint_presentation(self, content: Dict[str, Any], template: str = "default") -> Dict[str, Any]:
        """Generate PowerPoint presentation with advanced features"""
        try:
            # Create new presentation
            prs = Presentation()
            
            # Set slide size
            prs.slide_width = PptxInches(16)
            prs.slide_height = PptxInches(9)
            
            # Title slide
            title_slide_layout = prs.slide_layouts[0]
            slide = prs.slides.add_slide(title_slide_layout)
            title = slide.shapes.title
            subtitle = slide.placeholders[1]
            
            title.text = content.get("title", "Presentation Title")
            subtitle.text = content.get("subtitle", "Generated by JARVIS AI")
            
            # Add content slides
            slides_content = content.get("slides", [])
            for slide_content in slides_content:
                slide_layout = prs.slide_layouts[slide_content.get("layout", 1)]
                slide = prs.slides.add_slide(slide_layout)
                
                # Add title
                if slide_content.get("title"):
                    slide.shapes.title.text = slide_content["title"]
                
                # Add content
                if slide_content.get("content"):
                    content_placeholder = slide.placeholders[1]
                    content_placeholder.text = slide_content["content"]
                
                # Add bullet points
                if slide_content.get("bullets"):
                    text_frame = slide.placeholders[1].text_frame
                    text_frame.clear()
                    
                    for bullet in slide_content["bullets"]:
                        p = text_frame.add_paragraph()
                        p.text = bullet
                        p.level = 0
                
                # Add images
                if slide_content.get("images"):
                    for image_info in slide_content["images"]:
                        if image_info.get("path"):
                            slide.shapes.add_picture(
                                image_info["path"],
                                PptxInches(image_info.get("left", 1)),
                                PptxInches(image_info.get("top", 1)),
                                PptxInches(image_info.get("width", 4)),
                                PptxInches(image_info.get("height", 3))
                            )
                
                # Add charts
                if slide_content.get("charts"):
                    for chart_data in slide_content["charts"]:
                        chart_image = await self.generate_chart(chart_data)
                        if chart_image:
                            slide.shapes.add_picture(
                                chart_image,
                                PptxInches(1),
                                PptxInches(2),
                                PptxInches(8),
                                PptxInches(5)
                            )
            
            # Save presentation
            ppt_path = tempfile.mktemp(suffix=".pptx")
            prs.save(ppt_path)
            
            # Convert to bytes
            with open(ppt_path, "rb") as f:
                ppt_bytes = f.read()
            
            # Cleanup
            os.unlink(ppt_path)
            
            return {
                "document_bytes": base64.b64encode(ppt_bytes).decode(),
                "filename": f"{content.get('title', 'presentation')}.pptx",
                "size": len(ppt_bytes),
                "slides": len(prs.slides)
            }
            
        except Exception as e:
            logger.error(f"PowerPoint generation error: {e}")
            return {"error": str(e)}
    
    async def generate_pdf_document(self, content: Dict[str, Any], template: str = "default") -> Dict[str, Any]:
        """Generate PDF document with advanced formatting"""
        try:
            # Create PDF
            pdf_path = tempfile.mktemp(suffix=".pdf")
            doc = SimpleDocTemplate(pdf_path, pagesize=letter)
            
            # Get styles
            styles = getSampleStyleSheet()
            story = []
            
            # Add title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                alignment=1  # Center
            )
            story.append(Paragraph(content.get("title", "PDF Document"), title_style))
            story.append(Spacer(1, 12))
            
            # Add subtitle
            if content.get("subtitle"):
                subtitle_style = ParagraphStyle(
                    'CustomSubtitle',
                    parent=styles['Heading2'],
                    fontSize=16,
                    spaceAfter=20,
                    alignment=1
                )
                story.append(Paragraph(content["subtitle"], subtitle_style))
                story.append(Spacer(1, 12))
            
            # Add sections
            sections = content.get("sections", [])
            for section in sections:
                # Add section heading
                if section.get("heading"):
                    heading_style = styles[f'Heading{section.get("level", 2)}']
                    story.append(Paragraph(section["heading"], heading_style))
                    story.append(Spacer(1, 12))
                
                # Add section content
                if section.get("content"):
                    if isinstance(section["content"], list):
                        for paragraph in section["content"]:
                            story.append(Paragraph(paragraph, styles['Normal']))
                            story.append(Spacer(1, 6))
                    else:
                        story.append(Paragraph(section["content"], styles['Normal']))
                        story.append(Spacer(1, 12))
                
                # Add tables
                if section.get("tables"):
                    for table_data in section["tables"]:
                        table = await self.create_pdf_table(table_data)
                        story.append(table)
                        story.append(Spacer(1, 12))
                
                # Add charts
                if section.get("charts"):
                    for chart_data in section["charts"]:
                        chart_image = await self.generate_chart(chart_data)
                        if chart_image:
                            story.append(Image(chart_image, width=6*inch, height=4*inch))
                            story.append(Spacer(1, 12))
            
            # Build PDF
            doc.build(story)
            
            # Convert to bytes
            with open(pdf_path, "rb") as f:
                pdf_bytes = f.read()
            
            # Cleanup
            os.unlink(pdf_path)
            
            return {
                "document_bytes": base64.b64encode(pdf_bytes).decode(),
                "filename": f"{content.get('title', 'document')}.pdf",
                "size": len(pdf_bytes),
                "pages": len(story) // 10  # Rough estimate
            }
            
        except Exception as e:
            logger.error(f"PDF generation error: {e}")
            return {"error": str(e)}
    
    async def generate_excel_spreadsheet(self, content: Dict[str, Any], template: str = "default") -> Dict[str, Any]:
        """Generate Excel spreadsheet with data analysis"""
        try:
            # Create DataFrame
            data = content.get("data", [])
            if not data:
                # Generate sample data if none provided
                data = [
                    {"Name": "Sample 1", "Value": 100, "Category": "A"},
                    {"Name": "Sample 2", "Value": 200, "Category": "B"},
                    {"Name": "Sample 3", "Value": 150, "Category": "A"}
                ]
            
            df = pd.DataFrame(data)
            
            # Save to Excel
            excel_path = tempfile.mktemp(suffix=".xlsx")
            
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                # Main data sheet
                df.to_excel(writer, sheet_name='Data', index=False)
                
                # Summary sheet if requested
                if content.get("include_summary"):
                    summary_df = df.describe()
                    summary_df.to_excel(writer, sheet_name='Summary')
                
                # Charts sheet if requested
                if content.get("include_charts"):
                    chart_data = df.select_dtypes(include=[np.number])
                    chart_data.to_excel(writer, sheet_name='Charts', index=False)
            
            # Convert to bytes
            with open(excel_path, "rb") as f:
                excel_bytes = f.read()
            
            # Cleanup
            os.unlink(excel_path)
            
            return {
                "document_bytes": base64.b64encode(excel_bytes).decode(),
                "filename": f"{content.get('title', 'spreadsheet')}.xlsx",
                "size": len(excel_bytes),
                "rows": len(df),
                "columns": len(df.columns)
            }
            
        except Exception as e:
            logger.error(f"Excel generation error: {e}")
            return {"error": str(e)}
    
    async def generate_infographic(self, content: Dict[str, Any], template: str = "default") -> Dict[str, Any]:
        """Generate infographic with visual elements"""
        try:
            # Create infographic canvas
            width, height = 800, 1200
            img = PILImage.new('RGB', (width, height), color='white')
            draw = ImageDraw.Draw(img)
            
            # Load fonts (fallback to default if not available)
            try:
                title_font = ImageFont.truetype("arial.ttf", 36)
                heading_font = ImageFont.truetype("arial.ttf", 24)
                text_font = ImageFont.truetype("arial.ttf", 16)
            except:
                title_font = ImageFont.load_default()
                heading_font = ImageFont.load_default()
                text_font = ImageFont.load_default()
            
            y_position = 50
            
            # Add title
            title = content.get("title", "Infographic Title")
            title_bbox = draw.textbbox((0, 0), title, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            draw.text(((width - title_width) // 2, y_position), title, fill='black', font=title_font)
            y_position += 80
            
            # Add sections
            sections = content.get("sections", [])
            for section in sections:
                # Section heading
                if section.get("heading"):
                    heading = section["heading"]
                    draw.text((50, y_position), heading, fill='darkblue', font=heading_font)
                    y_position += 40
                
                # Section content
                if section.get("content"):
                    content_text = section["content"]
                    # Word wrap for long text
                    words = content_text.split()
                    lines = []
                    current_line = []
                    
                    for word in words:
                        test_line = ' '.join(current_line + [word])
                        test_bbox = draw.textbbox((0, 0), test_line, font=text_font)
                        if test_bbox[2] - test_bbox[0] <= width - 100:
                            current_line.append(word)
                        else:
                            if current_line:
                                lines.append(' '.join(current_line))
                            current_line = [word]
                    
                    if current_line:
                        lines.append(' '.join(current_line))
                    
                    for line in lines:
                        draw.text((50, y_position), line, fill='black', font=text_font)
                        y_position += 25
                
                y_position += 30
                
                # Add simple charts/visualizations
                if section.get("data"):
                    chart_height = 150
                    chart_y = y_position
                    
                    # Simple bar chart
                    data_values = list(section["data"].values())
                    if data_values and all(isinstance(v, (int, float)) for v in data_values):
                        max_value = max(data_values)
                        bar_width = (width - 100) // len(data_values)
                        
                        for i, (label, value) in enumerate(section["data"].items()):
                            bar_height = int((value / max_value) * chart_height)
                            x = 50 + i * bar_width
                            
                            # Draw bar
                            draw.rectangle([x, chart_y + chart_height - bar_height, 
                                          x + bar_width - 10, chart_y + chart_height], 
                                         fill='lightblue', outline='darkblue')
                            
                            # Draw label
                            label_bbox = draw.textbbox((0, 0), label, font=text_font)
                            label_width = label_bbox[2] - label_bbox[0]
                            draw.text((x + (bar_width - label_width) // 2, chart_y + chart_height + 10), 
                                    label, fill='black', font=text_font)
                    
                    y_position += chart_height + 50
            
            # Save infographic
            img_path = tempfile.mktemp(suffix=".png")
            img.save(img_path, "PNG")
            
            # Convert to bytes
            with open(img_path, "rb") as f:
                img_bytes = f.read()
            
            # Cleanup
            os.unlink(img_path)
            
            return {
                "document_bytes": base64.b64encode(img_bytes).decode(),
                "filename": f"{content.get('title', 'infographic')}.png",
                "size": len(img_bytes),
                "dimensions": f"{width}x{height}"
            }
            
        except Exception as e:
            logger.error(f"Infographic generation error: {e}")
            return {"error": str(e)}
    
    async def generate_ai_content(self, content_request: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI-powered content for documents"""
        try:
            # This would integrate with OpenAI or other AI services
            topic = content_request.get("topic", "General Topic")
            content_type = content_request.get("content_type", "article")
            length = content_request.get("length", "medium")
            
            # Simulate AI content generation
            ai_content = {
                "title": f"AI-Generated Content: {topic}",
                "subtitle": f"Comprehensive {content_type} on {topic}",
                "sections": [
                    {
                        "heading": "Introduction",
                        "content": f"This document provides a comprehensive overview of {topic}. "
                                 f"The following sections will explore various aspects and provide detailed insights."
                    },
                    {
                        "heading": "Main Content",
                        "content": f"The main discussion about {topic} covers several key points that are essential "
                                 f"for understanding the subject matter. This analysis provides valuable insights."
                    },
                    {
                        "heading": "Conclusion",
                        "content": f"In conclusion, {topic} represents an important area that requires careful consideration "
                                 f"and continued research to fully understand its implications."
                    }
                ]
            }
            
            return ai_content
            
        except Exception as e:
            logger.error(f"AI content generation error: {e}")
            return {"title": "Content Generation Error", "sections": []}
    
    async def generate_chart(self, chart_data: Dict[str, Any]) -> Optional[str]:
        """Generate chart image from data"""
        try:
            chart_type = chart_data.get("type", "bar")
            data = chart_data.get("data", {})
            title = chart_data.get("title", "Chart")
            
            if not data:
                return None
            
            # Create chart
            plt.figure(figsize=(10, 6))
            
            if chart_type == "bar":
                plt.bar(data.keys(), data.values())
            elif chart_type == "line":
                plt.plot(list(data.keys()), list(data.values()), marker='o')
            elif chart_type == "pie":
                plt.pie(data.values(), labels=data.keys(), autopct='%1.1f%%')
            
            plt.title(title)
            plt.tight_layout()
            
            # Save chart
            chart_path = tempfile.mktemp(suffix=".png")
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_path
            
        except Exception as e:
            logger.error(f"Chart generation error: {e}")
            return None
    
    async def load_templates(self):
        """Load document templates"""
        try:
            self.templates = {
                "default": {"style": "professional", "colors": ["blue", "white"]},
                "business": {"style": "corporate", "colors": ["navy", "gray", "white"]},
                "creative": {"style": "modern", "colors": ["purple", "orange", "white"]},
                "academic": {"style": "formal", "colors": ["black", "white"]}
            }
            logger.info("Document templates loaded")
        except Exception as e:
            logger.error(f"Template loading error: {e}")
    
    async def initialize_ai_generator(self):
        """Initialize AI content generator"""
        try:
            # This would initialize AI models for content generation
            logger.info("AI content generator initialized")
        except Exception as e:
            logger.error(f"AI generator initialization error: {e}")
    
    async def store_generation_history(self, user_id: str, generation_result: Dict[str, Any]):
        """Store document generation history"""
        try:
            history_key = f"doc_generation_history:{user_id}"
            await redis_client.lpush(history_key, generation_result)
            # Keep only last 50 generations
            await redis_client.ltrim(history_key, 0, 49)
        except Exception as e:
            logger.error(f"History storage error: {e}")
    
    async def cleanup(self):
        """Cleanup document generation service"""
        logger.info("Document Generation Service cleanup completed")
