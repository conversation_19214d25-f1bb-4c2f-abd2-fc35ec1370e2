"""
Voice processing router for JARVIS
"""

import asyncio
from typing import Dict, List, Any, Optional
from fastapi import API<PERSON>outer, Depends, HTTPException, UploadFile, File, Form
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_db, User
from ..core.security import get_current_active_user
from ..services.voice_service import VoiceService

router = APIRouter()

# Global voice service instance
voice_service = VoiceService()

class VoiceCommand(BaseModel):
    text: str
    language: str = "en"
    confidence: float
    duration: float

class TTSRequest(BaseModel):
    text: str
    language: str = "en"
    voice_speed: float = 1.0
    voice_pitch: float = 1.0

class VoiceAnalysis(BaseModel):
    duration: float
    loudness: float
    confidence: float
    language: str
    features: Dict[str, Any]

@router.on_event("startup")
async def startup_voice_service():
    """Initialize voice service on startup"""
    await voice_service.initialize()

@router.post("/speech-to-text", response_model=VoiceCommand)
async def speech_to_text(
    audio_file: UploadFile = File(...),
    language: str = Form("en"),
    use_whisper: bool = Form(True),
    current_user: User = Depends(get_current_active_user)
):
    """Convert speech to text"""
    
    try:
        # Read audio file
        audio_data = await audio_file.read()
        
        # Process with voice service
        result = await voice_service.speech_to_text(
            audio_data=audio_data,
            language=language,
            use_whisper=use_whisper
        )
        
        return VoiceCommand(
            text=result.get("text", ""),
            language=result.get("language", language),
            confidence=result.get("confidence", 0.0),
            duration=0.0  # Would be calculated from audio
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Speech recognition error: {str(e)}")

@router.post("/text-to-speech")
async def text_to_speech(
    request: TTSRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Convert text to speech"""
    
    try:
        audio_data = await voice_service.text_to_speech(
            text=request.text,
            language=request.language,
            voice_speed=request.voice_speed,
            voice_pitch=request.voice_pitch
        )
        
        if not audio_data:
            raise HTTPException(status_code=500, detail="Failed to generate speech")
        
        from fastapi.responses import Response
        return Response(
            content=audio_data,
            media_type="audio/wav",
            headers={"Content-Disposition": "attachment; filename=speech.wav"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Text-to-speech error: {str(e)}")

@router.post("/analyze-voice", response_model=VoiceAnalysis)
async def analyze_voice(
    audio_file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Analyze voice characteristics"""
    
    try:
        audio_data = await audio_file.read()
        
        analysis = await voice_service.analyze_voice(audio_data)
        
        return VoiceAnalysis(
            duration=analysis.get("duration", 0.0),
            loudness=analysis.get("loudness", 0.0),
            confidence=0.95,  # Placeholder
            language="en",  # Would be detected
            features=analysis
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Voice analysis error: {str(e)}")

@router.post("/create-voice-profile")
async def create_voice_profile(
    audio_files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Create voice profile for user"""
    
    try:
        if len(audio_files) < 3:
            raise HTTPException(
                status_code=400,
                detail="At least 3 audio samples required for voice profile"
            )
        
        audio_samples = []
        for audio_file in audio_files:
            audio_data = await audio_file.read()
            audio_samples.append(audio_data)
        
        profile = await voice_service.create_voice_profile(
            user_id=str(current_user.id),
            audio_samples=audio_samples
        )
        
        return {
            "message": "Voice profile created successfully",
            "profile": profile
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Voice profile creation error: {str(e)}")

@router.post("/verify-voice")
async def verify_voice(
    audio_file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Verify voice against stored profile"""
    
    try:
        audio_data = await audio_file.read()
        
        verification = await voice_service.verify_voice(
            user_id=str(current_user.id),
            audio_data=audio_data
        )
        
        return verification
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Voice verification error: {str(e)}")

@router.post("/record-audio")
async def record_audio(
    duration: float = 5.0,
    current_user: User = Depends(get_current_active_user)
):
    """Record audio from server microphone (if available)"""
    
    try:
        audio_data = await voice_service.record_audio(duration=duration)
        
        if not audio_data:
            raise HTTPException(status_code=500, detail="Failed to record audio")
        
        from fastapi.responses import Response
        return Response(
            content=audio_data,
            media_type="audio/wav",
            headers={"Content-Disposition": "attachment; filename=recording.wav"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Audio recording error: {str(e)}")

@router.get("/supported-languages")
async def get_supported_languages():
    """Get list of supported languages"""
    
    return {
        "languages": voice_service.supported_languages,
        "default": "en",
        "tts_languages": [
            {"code": "en", "name": "English"},
            {"code": "es", "name": "Spanish"},
            {"code": "fr", "name": "French"},
            {"code": "de", "name": "German"},
            {"code": "it", "name": "Italian"},
            {"code": "pt", "name": "Portuguese"},
            {"code": "ru", "name": "Russian"},
            {"code": "ja", "name": "Japanese"},
            {"code": "ko", "name": "Korean"},
            {"code": "zh", "name": "Chinese"}
        ]
    }

@router.get("/voice-status")
async def get_voice_status():
    """Get voice service status"""
    
    return {
        "status": "operational",
        "features": [
            "speech_to_text",
            "text_to_speech",
            "voice_analysis",
            "voice_profiles",
            "voice_verification",
            "multi_language_support"
        ],
        "engines": [
            "whisper",
            "google_speech",
            "gtts"
        ],
        "microphone_available": voice_service.microphone is not None
    }
