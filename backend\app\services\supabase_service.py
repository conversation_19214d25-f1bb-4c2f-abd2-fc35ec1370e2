"""
Supabase Cloud Database Service for JARVIS
Handles cloud storage, real-time sync, and advanced database operations
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from supabase import create_client, Client
from loguru import logger

from ..core.config import settings

class SupabaseService:
    """Cloud database service using Supabase"""
    
    def __init__(self):
        self.supabase: Optional[Client] = None
        self.url = settings.SUPABASE_URL if hasattr(settings, 'SUPABASE_URL') else None
        self.key = settings.SUPABASE_KEY if hasattr(settings, 'SUPABASE_KEY') else None
        
    async def initialize(self):
        """Initialize Supabase connection"""
        try:
            if self.url and self.key:
                self.supabase = create_client(self.url, self.key)
                logger.info("Supabase service initialized")
            else:
                logger.warning("Supabase credentials not provided, using local storage")
        except Exception as e:
            logger.error(f"Failed to initialize Supabase: {e}")
    
    async def store_user_data(self, user_id: str, data_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Store user data in cloud database"""
        try:
            if not self.supabase:
                return {"error": "Supabase not initialized"}
            
            record = {
                "user_id": user_id,
                "data_type": data_type,
                "data": data,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            result = self.supabase.table("user_data").insert(record).execute()
            
            return {
                "message": "Data stored successfully",
                "record_id": result.data[0]["id"] if result.data else None
            }
            
        except Exception as e:
            logger.error(f"Store user data error: {e}")
            return {"error": "Failed to store user data"}
    
    async def get_user_data(self, user_id: str, data_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Retrieve user data from cloud database"""
        try:
            if not self.supabase:
                return []
            
            query = self.supabase.table("user_data").select("*").eq("user_id", user_id)
            
            if data_type:
                query = query.eq("data_type", data_type)
            
            result = query.execute()
            
            return result.data or []
            
        except Exception as e:
            logger.error(f"Get user data error: {e}")
            return []
    
    async def sync_preferences(self, user_id: str, preferences: Dict[str, Any]) -> Dict[str, Any]:
        """Sync user preferences to cloud"""
        try:
            if not self.supabase:
                return {"error": "Supabase not initialized"}
            
            # Check if preferences already exist
            existing = self.supabase.table("user_preferences").select("*").eq("user_id", user_id).execute()
            
            preference_record = {
                "user_id": user_id,
                "preferences": preferences,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            if existing.data:
                # Update existing preferences
                result = self.supabase.table("user_preferences").update(preference_record).eq("user_id", user_id).execute()
            else:
                # Insert new preferences
                preference_record["created_at"] = datetime.utcnow().isoformat()
                result = self.supabase.table("user_preferences").insert(preference_record).execute()
            
            return {
                "message": "Preferences synced successfully",
                "synced_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Sync preferences error: {e}")
            return {"error": "Failed to sync preferences"}
    
    async def store_conversation_history(self, user_id: str, conversation: Dict[str, Any]) -> Dict[str, Any]:
        """Store conversation history in cloud"""
        try:
            if not self.supabase:
                return {"error": "Supabase not initialized"}
            
            conversation_record = {
                "user_id": user_id,
                "session_id": conversation.get("session_id"),
                "message": conversation.get("message"),
                "response": conversation.get("response"),
                "intent": conversation.get("intent"),
                "context": conversation.get("context", {}),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            result = self.supabase.table("conversations").insert(conversation_record).execute()
            
            return {
                "message": "Conversation stored successfully",
                "record_id": result.data[0]["id"] if result.data else None
            }
            
        except Exception as e:
            logger.error(f"Store conversation error: {e}")
            return {"error": "Failed to store conversation"}
    
    async def get_conversation_history(self, user_id: str, session_id: Optional[str] = None, 
                                     limit: int = 50) -> List[Dict[str, Any]]:
        """Get conversation history from cloud"""
        try:
            if not self.supabase:
                return []
            
            query = self.supabase.table("conversations").select("*").eq("user_id", user_id)
            
            if session_id:
                query = query.eq("session_id", session_id)
            
            result = query.order("timestamp", desc=True).limit(limit).execute()
            
            return result.data or []
            
        except Exception as e:
            logger.error(f"Get conversation history error: {e}")
            return []
    
    async def store_health_record(self, user_id: str, record_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Store health records in cloud"""
        try:
            if not self.supabase:
                return {"error": "Supabase not initialized"}
            
            health_record = {
                "user_id": user_id,
                "record_type": record_type,
                "data": data,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            result = self.supabase.table("health_records").insert(health_record).execute()
            
            return {
                "message": "Health record stored successfully",
                "record_id": result.data[0]["id"] if result.data else None
            }
            
        except Exception as e:
            logger.error(f"Store health record error: {e}")
            return {"error": "Failed to store health record"}
    
    async def get_health_records(self, user_id: str, record_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get health records from cloud"""
        try:
            if not self.supabase:
                return []
            
            query = self.supabase.table("health_records").select("*").eq("user_id", user_id)
            
            if record_type:
                query = query.eq("record_type", record_type)
            
            result = query.order("timestamp", desc=True).execute()
            
            return result.data or []
            
        except Exception as e:
            logger.error(f"Get health records error: {e}")
            return []
    
    async def store_booking(self, user_id: str, booking: Dict[str, Any]) -> Dict[str, Any]:
        """Store booking information in cloud"""
        try:
            if not self.supabase:
                return {"error": "Supabase not initialized"}
            
            booking_record = {
                "user_id": user_id,
                "booking_id": booking.get("booking_id"),
                "booking_type": booking.get("type"),
                "booking_data": booking,
                "status": booking.get("status", "active"),
                "created_at": datetime.utcnow().isoformat()
            }
            
            result = self.supabase.table("bookings").insert(booking_record).execute()
            
            return {
                "message": "Booking stored successfully",
                "record_id": result.data[0]["id"] if result.data else None
            }
            
        except Exception as e:
            logger.error(f"Store booking error: {e}")
            return {"error": "Failed to store booking"}
    
    async def get_user_bookings(self, user_id: str, booking_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get user bookings from cloud"""
        try:
            if not self.supabase:
                return []
            
            query = self.supabase.table("bookings").select("*").eq("user_id", user_id)
            
            if booking_type:
                query = query.eq("booking_type", booking_type)
            
            result = query.order("created_at", desc=True).execute()
            
            return result.data or []
            
        except Exception as e:
            logger.error(f"Get user bookings error: {e}")
            return []
    
    async def store_device_data(self, user_id: str, device_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Store device data and sensor readings"""
        try:
            if not self.supabase:
                return {"error": "Supabase not initialized"}
            
            device_record = {
                "user_id": user_id,
                "device_id": device_id,
                "device_type": data.get("device_type"),
                "sensor_data": data.get("sensor_data", {}),
                "status": data.get("status", "online"),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            result = self.supabase.table("device_data").insert(device_record).execute()
            
            return {
                "message": "Device data stored successfully",
                "record_id": result.data[0]["id"] if result.data else None
            }
            
        except Exception as e:
            logger.error(f"Store device data error: {e}")
            return {"error": "Failed to store device data"}
    
    async def get_analytics_data(self, user_id: str, metric_type: str, 
                               start_date: Optional[str] = None, end_date: Optional[str] = None) -> Dict[str, Any]:
        """Get analytics data for user behavior analysis"""
        try:
            if not self.supabase:
                return {"error": "Supabase not initialized"}
            
            # Query different tables based on metric type
            if metric_type == "conversations":
                query = self.supabase.table("conversations").select("*").eq("user_id", user_id)
            elif metric_type == "health":
                query = self.supabase.table("health_records").select("*").eq("user_id", user_id)
            elif metric_type == "bookings":
                query = self.supabase.table("bookings").select("*").eq("user_id", user_id)
            elif metric_type == "devices":
                query = self.supabase.table("device_data").select("*").eq("user_id", user_id)
            else:
                return {"error": "Invalid metric type"}
            
            if start_date:
                query = query.gte("timestamp", start_date)
            if end_date:
                query = query.lte("timestamp", end_date)
            
            result = query.execute()
            
            # Process data for analytics
            analytics = self.process_analytics_data(result.data or [], metric_type)
            
            return {
                "metric_type": metric_type,
                "data_points": len(result.data or []),
                "analytics": analytics,
                "period": {
                    "start": start_date,
                    "end": end_date
                }
            }
            
        except Exception as e:
            logger.error(f"Get analytics data error: {e}")
            return {"error": "Failed to get analytics data"}
    
    def process_analytics_data(self, data: List[Dict[str, Any]], metric_type: str) -> Dict[str, Any]:
        """Process raw data into analytics insights"""
        if not data:
            return {}
        
        analytics = {
            "total_records": len(data),
            "date_range": {
                "start": min(record.get("timestamp", "") for record in data),
                "end": max(record.get("timestamp", "") for record in data)
            }
        }
        
        if metric_type == "conversations":
            # Conversation analytics
            analytics.update({
                "total_conversations": len(data),
                "avg_conversations_per_day": len(data) / max(1, len(set(record.get("timestamp", "")[:10] for record in data))),
                "most_common_intents": self.get_most_common_values(data, "intent")
            })
        
        elif metric_type == "health":
            # Health analytics
            analytics.update({
                "total_health_records": len(data),
                "record_types": self.get_most_common_values(data, "record_type"),
                "health_trends": self.analyze_health_trends(data)
            })
        
        elif metric_type == "bookings":
            # Booking analytics
            analytics.update({
                "total_bookings": len(data),
                "booking_types": self.get_most_common_values(data, "booking_type"),
                "booking_success_rate": len([b for b in data if b.get("status") == "confirmed"]) / len(data)
            })
        
        return analytics
    
    def get_most_common_values(self, data: List[Dict[str, Any]], field: str) -> List[Dict[str, Any]]:
        """Get most common values for a field"""
        from collections import Counter
        
        values = [record.get(field) for record in data if record.get(field)]
        counter = Counter(values)
        
        return [{"value": value, "count": count} for value, count in counter.most_common(5)]
    
    def analyze_health_trends(self, health_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze health trends from data"""
        # This would implement more sophisticated health trend analysis
        return {
            "trend": "stable",
            "improvement_areas": [],
            "risk_factors": []
        }
    
    async def backup_user_data(self, user_id: str) -> Dict[str, Any]:
        """Create a complete backup of user data"""
        try:
            if not self.supabase:
                return {"error": "Supabase not initialized"}
            
            backup_data = {}
            
            # Backup all user data types
            tables = ["user_data", "user_preferences", "conversations", "health_records", "bookings", "device_data"]
            
            for table in tables:
                result = self.supabase.table(table).select("*").eq("user_id", user_id).execute()
                backup_data[table] = result.data or []
            
            # Store backup record
            backup_record = {
                "user_id": user_id,
                "backup_data": backup_data,
                "created_at": datetime.utcnow().isoformat()
            }
            
            self.supabase.table("user_backups").insert(backup_record).execute()
            
            return {
                "message": "User data backed up successfully",
                "backup_size": sum(len(data) for data in backup_data.values()),
                "tables_backed_up": len(tables)
            }
            
        except Exception as e:
            logger.error(f"Backup user data error: {e}")
            return {"error": "Failed to backup user data"}
    
    async def cleanup(self):
        """Cleanup Supabase service"""
        logger.info("Supabase service cleanup completed")
