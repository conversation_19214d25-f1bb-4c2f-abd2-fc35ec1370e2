import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import axios from 'axios';

interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  is_active: boolean;
  is_superuser: boolean;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  register: (userData: any) => Promise<boolean>;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
}

const API_BASE_URL = 'http://localhost:8000/api/v1';

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,

      login: async (username: string, password: string) => {
        try {
          const formData = new FormData();
          formData.append('username', username);
          formData.append('password', password);

          const response = await axios.post(`${API_BASE_URL}/auth/login`, formData, {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          });

          const { access_token, user } = response.data;

          set({
            token: access_token,
            user,
            isAuthenticated: true,
          });

          // Set default authorization header
          axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;

          return true;
        } catch (error) {
          console.error('Login failed:', error);
          return false;
        }
      },

      register: async (userData: any) => {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/register`, userData);
          const { access_token, user } = response.data;

          set({
            token: access_token,
            user,
            isAuthenticated: true,
          });

          axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;

          return true;
        } catch (error) {
          console.error('Registration failed:', error);
          return false;
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        });

        delete axios.defaults.headers.common['Authorization'];
      },

      refreshToken: async () => {
        try {
          const { token } = get();
          if (!token) return false;

          const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {}, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          const { access_token, user } = response.data;

          set({
            token: access_token,
            user,
            isAuthenticated: true,
          });

          axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;

          return true;
        } catch (error) {
          console.error('Token refresh failed:', error);
          get().logout();
          return false;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Initialize axios interceptor
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      const { refreshToken, logout } = useAuthStore.getState();
      const success = await refreshToken();
      if (!success) {
        logout();
      }
    }
    return Promise.reject(error);
  }
);
