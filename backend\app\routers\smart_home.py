"""Smart Home integration router"""

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from ..core.database import User
from ..core.security import get_current_active_user

router = APIRouter()

class SmartHomeCommand(BaseModel):
    device_type: str
    action: str
    value: str = ""

@router.post("/control")
async def control_smart_home(
    command: SmartHomeCommand,
    current_user: User = Depends(get_current_active_user)
):
    return {"message": f"Controlled {command.device_type}: {command.action}", "status": "success"}

@router.get("/devices")
async def list_smart_devices(current_user: User = Depends(get_current_active_user)):
    return {"devices": [{"name": "Living Room Lights", "type": "light", "status": "on"}]}
