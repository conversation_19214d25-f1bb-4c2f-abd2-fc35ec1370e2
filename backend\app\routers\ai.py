"""
AI and NLP router for JARVIS
"""

import asyncio
from typing import Dict, List, Any, Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_db, User, Conversation
from ..core.security import get_current_active_user
from ..services.ai_service import AIService

router = APIRouter()

# Global AI service instance
ai_service = AIService()

class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    intent: Optional[Dict[str, Any]] = None
    suggestions: List[str] = []

class ConversationHistory(BaseModel):
    id: int
    message: str
    response: str
    message_type: str
    created_at: str
    metadata: Dict[str, Any]

@router.on_event("startup")
async def startup_ai_service():
    """Initialize AI service on startup"""
    await ai_service.initialize()

@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai(
    request: ChatRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Chat with JARVIS AI"""
    
    try:
        # Process the message with AI
        user_dict = {
            "id": current_user.id,
            "username": current_user.username,
            "preferences": current_user.preferences or {}
        }
        
        response = await ai_service.process_command(
            request.message,
            user_dict,
            request.context
        )
        
        # Generate session ID if not provided
        session_id = request.session_id or f"session_{current_user.id}_{asyncio.get_event_loop().time()}"
        
        # Save conversation to database in background
        background_tasks.add_task(
            save_conversation,
            db,
            current_user.id,
            session_id,
            request.message,
            response,
            "text",
            request.context or {}
        )
        
        # Generate suggestions
        suggestions = await generate_suggestions(request.message, response)
        
        return ChatResponse(
            response=response,
            session_id=session_id,
            suggestions=suggestions
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI processing error: {str(e)}")

@router.post("/analyze-intent")
async def analyze_intent(
    request: ChatRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Analyze the intent of a message"""
    
    try:
        intent = await ai_service._analyze_intent(request.message)
        return {"intent": intent}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Intent analysis error: {str(e)}")

@router.get("/conversation-history", response_model=List[ConversationHistory])
async def get_conversation_history(
    session_id: Optional[str] = None,
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get conversation history"""
    
    try:
        from sqlalchemy import select, desc
        
        query = select(Conversation).where(Conversation.user_id == current_user.id)
        
        if session_id:
            query = query.where(Conversation.session_id == session_id)
        
        query = query.order_by(desc(Conversation.created_at)).limit(limit)
        
        result = await db.execute(query)
        conversations = result.scalars().all()
        
        return [
            ConversationHistory(
                id=conv.id,
                message=conv.message,
                response=conv.response or "",
                message_type=conv.message_type,
                created_at=conv.created_at.isoformat(),
                metadata=conv.metadata or {}
            )
            for conv in conversations
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.delete("/conversation-history")
async def clear_conversation_history(
    session_id: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Clear conversation history"""
    
    try:
        from sqlalchemy import delete
        
        query = delete(Conversation).where(Conversation.user_id == current_user.id)
        
        if session_id:
            query = query.where(Conversation.session_id == session_id)
        
        await db.execute(query)
        await db.commit()
        
        return {"message": "Conversation history cleared"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.post("/generate-response")
async def generate_response(
    request: ChatRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Generate a response without saving to conversation history"""
    
    try:
        user_dict = {
            "id": current_user.id,
            "username": current_user.username,
            "preferences": current_user.preferences or {}
        }
        
        response = await ai_service.process_command(
            request.message,
            user_dict,
            request.context
        )
        
        return {"response": response}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI processing error: {str(e)}")

@router.post("/train-model")
async def train_model(
    training_data: List[Dict[str, str]],
    current_user: User = Depends(get_current_active_user)
):
    """Train AI model with custom data (placeholder for future implementation)"""
    
    # This would implement custom model training
    # For now, return a placeholder response
    
    return {
        "message": "Model training initiated",
        "training_samples": len(training_data),
        "status": "queued"
    }

@router.get("/ai-status")
async def get_ai_status():
    """Get AI service status"""
    
    return {
        "status": "operational",
        "model": "gpt-4-turbo-preview",
        "features": [
            "natural_language_processing",
            "intent_analysis",
            "conversation_management",
            "context_awareness",
            "multi_language_support"
        ],
        "capabilities": [
            "device_control",
            "smart_home_integration",
            "information_retrieval",
            "automation",
            "communication",
            "entertainment"
        ]
    }

async def save_conversation(
    db: AsyncSession,
    user_id: int,
    session_id: str,
    message: str,
    response: str,
    message_type: str,
    metadata: Dict[str, Any]
):
    """Save conversation to database"""
    
    try:
        conversation = Conversation(
            user_id=user_id,
            session_id=session_id,
            message=message,
            response=response,
            message_type=message_type,
            metadata=metadata
        )
        
        db.add(conversation)
        await db.commit()
        
    except Exception as e:
        print(f"Error saving conversation: {e}")

async def generate_suggestions(message: str, response: str) -> List[str]:
    """Generate follow-up suggestions"""
    
    # Simple suggestion generation based on keywords
    suggestions = []
    
    message_lower = message.lower()
    
    if "weather" in message_lower:
        suggestions.extend([
            "What's the weather forecast for tomorrow?",
            "Set a weather alert for rain",
            "Show me the weekly weather forecast"
        ])
    elif "music" in message_lower:
        suggestions.extend([
            "Play my favorite playlist",
            "What's trending in music?",
            "Create a new playlist"
        ])
    elif "lights" in message_lower or "smart home" in message_lower:
        suggestions.extend([
            "Turn off all lights",
            "Set mood lighting",
            "Show me energy usage"
        ])
    elif "schedule" in message_lower or "calendar" in message_lower:
        suggestions.extend([
            "What's my schedule for tomorrow?",
            "Schedule a meeting",
            "Set a reminder"
        ])
    else:
        suggestions.extend([
            "What can you help me with?",
            "Show me my recent activities",
            "What's new today?"
        ])
    
    return suggestions[:3]  # Return top 3 suggestions
