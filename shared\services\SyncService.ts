/**
 * Cross-Platform Synchronization Service
 * Ensures seamless data sync between mobile, desktop, and web platforms
 */

import { EventEmitter } from 'events';
import io, { Socket } from 'socket.io-client';

export interface SyncData {
  id: string;
  type: 'user_preferences' | 'conversation_history' | 'automation_rules' | 'voice_profiles' | 'system_state';
  data: any;
  timestamp: number;
  deviceId: string;
  platform: 'mobile' | 'desktop' | 'web';
  version: number;
}

export interface DeviceInfo {
  id: string;
  name: string;
  platform: 'mobile' | 'desktop' | 'web';
  version: string;
  lastSeen: number;
  isOnline: boolean;
  capabilities: string[];
}

export interface SyncConflict {
  id: string;
  type: string;
  localData: any;
  remoteData: any;
  localTimestamp: number;
  remoteTimestamp: number;
  resolution?: 'local' | 'remote' | 'merge';
}

export class CrossPlatformSyncService extends EventEmitter {
  private socket: Socket | null = null;
  private deviceId: string;
  private platform: 'mobile' | 'desktop' | 'web';
  private isConnected: boolean = false;
  private syncQueue: SyncData[] = [];
  private localData: Map<string, SyncData> = new Map();
  private conflictResolver: ((conflict: SyncConflict) => Promise<'local' | 'remote' | 'merge'>) | null = null;
  private syncInterval: NodeJS.Timeout | null = null;
  private retryAttempts: number = 0;
  private maxRetryAttempts: number = 5;

  constructor(deviceId: string, platform: 'mobile' | 'desktop' | 'web') {
    super();
    this.deviceId = deviceId;
    this.platform = platform;
  }

  async initialize(serverUrl: string, authToken?: string): Promise<void> {
    try {
      // Initialize socket connection
      this.socket = io(serverUrl, {
        auth: {
          token: authToken,
          deviceId: this.deviceId,
          platform: this.platform,
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxRetryAttempts,
        reconnectionDelay: 1000,
      });

      this.setupSocketListeners();
      
      // Start periodic sync
      this.startPeriodicSync();

      console.log('Cross-platform sync service initialized');
    } catch (error) {
      console.error('Failed to initialize sync service:', error);
      throw error;
    }
  }

  private setupSocketListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      this.isConnected = true;
      this.retryAttempts = 0;
      this.emit('connected');
      
      // Send queued data
      this.processQueue();
      
      console.log('Connected to sync server');
    });

    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      this.emit('disconnected', reason);
      console.log('Disconnected from sync server:', reason);
    });

    this.socket.on('sync_data', (data: SyncData) => {
      this.handleIncomingSync(data);
    });

    this.socket.on('sync_conflict', (conflict: SyncConflict) => {
      this.handleSyncConflict(conflict);
    });

    this.socket.on('device_status', (devices: DeviceInfo[]) => {
      this.emit('devices_updated', devices);
    });

    this.socket.on('sync_error', (error: any) => {
      console.error('Sync error:', error);
      this.emit('sync_error', error);
    });

    this.socket.on('connect_error', (error) => {
      this.retryAttempts++;
      console.error('Connection error:', error);
      
      if (this.retryAttempts >= this.maxRetryAttempts) {
        this.emit('connection_failed', error);
      }
    });
  }

  async syncData(type: SyncData['type'], data: any): Promise<void> {
    const syncData: SyncData = {
      id: this.generateId(),
      type,
      data,
      timestamp: Date.now(),
      deviceId: this.deviceId,
      platform: this.platform,
      version: 1,
    };

    // Store locally
    this.localData.set(syncData.id, syncData);

    if (this.isConnected && this.socket) {
      // Send immediately
      this.socket.emit('sync_data', syncData);
    } else {
      // Queue for later
      this.syncQueue.push(syncData);
    }

    this.emit('data_synced', syncData);
  }

  async getData(type: SyncData['type']): Promise<any[]> {
    const results: any[] = [];
    
    for (const [id, syncData] of this.localData) {
      if (syncData.type === type) {
        results.push(syncData.data);
      }
    }

    return results;
  }

  async getLatestData(type: SyncData['type']): Promise<any | null> {
    let latest: SyncData | null = null;
    
    for (const [id, syncData] of this.localData) {
      if (syncData.type === type) {
        if (!latest || syncData.timestamp > latest.timestamp) {
          latest = syncData;
        }
      }
    }

    return latest ? latest.data : null;
  }

  private async handleIncomingSync(remoteData: SyncData): Promise<void> {
    const existingData = this.localData.get(remoteData.id);

    if (!existingData) {
      // New data, store it
      this.localData.set(remoteData.id, remoteData);
      this.emit('data_received', remoteData);
      return;
    }

    // Check for conflicts
    if (existingData.timestamp !== remoteData.timestamp) {
      const conflict: SyncConflict = {
        id: remoteData.id,
        type: remoteData.type,
        localData: existingData.data,
        remoteData: remoteData.data,
        localTimestamp: existingData.timestamp,
        remoteTimestamp: remoteData.timestamp,
      };

      await this.handleSyncConflict(conflict);
    }
  }

  private async handleSyncConflict(conflict: SyncConflict): Promise<void> {
    let resolution: 'local' | 'remote' | 'merge' = 'remote';

    if (this.conflictResolver) {
      resolution = await this.conflictResolver(conflict);
    } else {
      // Default resolution: use most recent
      resolution = conflict.remoteTimestamp > conflict.localTimestamp ? 'remote' : 'local';
    }

    const resolvedData = await this.resolveConflict(conflict, resolution);
    
    if (resolvedData) {
      this.localData.set(conflict.id, resolvedData);
      this.emit('conflict_resolved', { conflict, resolution, data: resolvedData });
    }
  }

  private async resolveConflict(
    conflict: SyncConflict, 
    resolution: 'local' | 'remote' | 'merge'
  ): Promise<SyncData | null> {
    switch (resolution) {
      case 'local':
        return this.localData.get(conflict.id) || null;
      
      case 'remote':
        return {
          id: conflict.id,
          type: conflict.type as SyncData['type'],
          data: conflict.remoteData,
          timestamp: conflict.remoteTimestamp,
          deviceId: this.deviceId,
          platform: this.platform,
          version: 1,
        };
      
      case 'merge':
        return await this.mergeData(conflict);
      
      default:
        return null;
    }
  }

  private async mergeData(conflict: SyncConflict): Promise<SyncData | null> {
    try {
      // Simple merge strategy - combine objects
      let mergedData: any;

      if (typeof conflict.localData === 'object' && typeof conflict.remoteData === 'object') {
        mergedData = { ...conflict.localData, ...conflict.remoteData };
      } else {
        // For non-objects, use the more recent one
        mergedData = conflict.remoteTimestamp > conflict.localTimestamp 
          ? conflict.remoteData 
          : conflict.localData;
      }

      return {
        id: conflict.id,
        type: conflict.type as SyncData['type'],
        data: mergedData,
        timestamp: Math.max(conflict.localTimestamp, conflict.remoteTimestamp),
        deviceId: this.deviceId,
        platform: this.platform,
        version: 1,
      };
    } catch (error) {
      console.error('Failed to merge data:', error);
      return null;
    }
  }

  private processQueue(): void {
    if (!this.isConnected || !this.socket) return;

    while (this.syncQueue.length > 0) {
      const data = this.syncQueue.shift();
      if (data) {
        this.socket.emit('sync_data', data);
      }
    }
  }

  private startPeriodicSync(): void {
    this.syncInterval = setInterval(() => {
      if (this.isConnected) {
        this.requestFullSync();
      }
    }, 30000); // Sync every 30 seconds
  }

  private requestFullSync(): void {
    if (this.socket) {
      this.socket.emit('request_full_sync', {
        deviceId: this.deviceId,
        platform: this.platform,
        lastSyncTime: this.getLastSyncTime(),
      });
    }
  }

  private getLastSyncTime(): number {
    let lastSync = 0;
    for (const [id, data] of this.localData) {
      if (data.timestamp > lastSync) {
        lastSync = data.timestamp;
      }
    }
    return lastSync;
  }

  setConflictResolver(
    resolver: (conflict: SyncConflict) => Promise<'local' | 'remote' | 'merge'>
  ): void {
    this.conflictResolver = resolver;
  }

  async getConnectedDevices(): Promise<DeviceInfo[]> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Not connected'));
        return;
      }

      this.socket.emit('get_devices', (devices: DeviceInfo[]) => {
        resolve(devices);
      });

      setTimeout(() => {
        reject(new Error('Timeout'));
      }, 5000);
    });
  }

  async clearLocalData(): Promise<void> {
    this.localData.clear();
    this.emit('data_cleared');
  }

  async exportData(): Promise<SyncData[]> {
    return Array.from(this.localData.values());
  }

  async importData(data: SyncData[]): Promise<void> {
    for (const item of data) {
      this.localData.set(item.id, item);
    }
    this.emit('data_imported', data.length);
  }

  private generateId(): string {
    return `${this.deviceId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async disconnect(): Promise<void> {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.emit('disconnected', 'manual');
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  getQueueSize(): number {
    return this.syncQueue.length;
  }

  getLocalDataSize(): number {
    return this.localData.size;
  }
}
