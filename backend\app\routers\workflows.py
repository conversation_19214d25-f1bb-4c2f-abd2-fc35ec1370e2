"""N8N workflow integration router"""

from fastapi import APIRouter, Depends
from ..core.database import User
from ..core.security import get_current_active_user

router = APIRouter()

@router.get("/list")
async def list_workflows(current_user: User = Depends(get_current_active_user)):
    return {"workflows": []}

@router.post("/trigger/{workflow_id}")
async def trigger_workflow(
    workflow_id: str,
    current_user: User = Depends(get_current_active_user)
):
    return {"message": "Workflow triggered", "id": workflow_id}
