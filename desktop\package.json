{"name": "jarvis-desktop", "version": "1.0.0", "description": "JARVIS Ultra-Advanced AI Assistant Desktop Application", "main": "dist/main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:react\" \"wait-on http://localhost:3000 && npm run dev:electron\"", "dev:react": "react-scripts start", "dev:electron": "cross-env NODE_ENV=development electron .", "build": "npm run build:react && npm run build:electron", "build:react": "react-scripts build", "build:electron": "tsc -p tsconfig.electron.json", "pack": "npm run build && electron-builder --dir", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "test": "react-scripts test", "eject": "react-scripts eject", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.jarvis.desktop", "productName": "JARVIS Desktop", "directories": {"output": "release"}, "files": ["dist/", "build/", "node_modules/", "package.json"], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "dependencies": {"@electron/remote": "^2.0.12", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.16", "@mui/material": "^5.14.17", "@mui/x-charts": "^6.18.1", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "@reduxjs/toolkit": "^1.9.7", "axios": "^1.5.0", "electron-store": "^8.1.0", "electron-updater": "^6.1.4", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.3", "react-router-dom": "^6.17.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.2", "three": "^0.157.0", "@react-three/fiber": "^8.15.11", "@react-three/drei": "^9.88.13", "recharts": "^2.8.0", "react-webcam": "^7.1.1", "react-speech-recognition": "^3.10.0", "react-use-gesture": "^9.1.3", "react-spring": "^9.7.3", "lottie-react": "^2.4.0", "react-hotkeys-hook": "^4.4.1", "react-draggable": "^4.4.6", "react-resizable": "^3.0.5", "react-grid-layout": "^1.4.4", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-ace": "^10.1.0", "ace-builds": "^1.32.2", "monaco-editor": "^0.44.0", "@monaco-editor/react": "^4.6.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@types/node": "^16.18.59", "@types/react": "^18.2.33", "@types/react-dom": "^18.2.14", "@types/three": "^0.157.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^27.0.2", "electron-builder": "^24.6.4", "wait-on": "^7.0.1"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["jarvis", "ai", "assistant", "desktop", "electron", "react", "voice", "computer-vision", "automation"], "author": "JARVIS Team", "license": "MIT"}