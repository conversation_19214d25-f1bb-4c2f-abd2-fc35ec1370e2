"""
Advanced Booking Service for JARVIS
Handles all types of bookings: flights, hotels, restaurants, appointments, etc.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import requests
from loguru import logger

from ..core.config import settings
from ..core.redis_client import redis_client

class BookingService:
    """Comprehensive booking and reservation service"""
    
    def __init__(self):
        self.booking_apis = {
            "flights": "https://api.amadeus.com/v2",
            "hotels": "https://api.booking.com/v1",
            "restaurants": "https://api.opentable.com/v2",
            "events": "https://api.eventbrite.com/v3",
            "transportation": "https://api.uber.com/v1.2",
            "healthcare": "https://api.zocdoc.com/v1"
        }
        self.active_bookings = {}
        
    async def initialize(self):
        """Initialize booking service"""
        logger.info("Booking service initialized")
        
    async def search_flights(self, origin: str, destination: str, departure_date: str, 
                           return_date: Optional[str] = None, passengers: int = 1) -> Dict[str, Any]:
        """Search for flights"""
        try:
            # Simulate flight search (would use real API like Amadeus)
            flights = [
                {
                    "id": "FL001",
                    "airline": "American Airlines",
                    "flight_number": "AA1234",
                    "origin": origin,
                    "destination": destination,
                    "departure_time": f"{departure_date}T08:00:00",
                    "arrival_time": f"{departure_date}T12:00:00",
                    "price": 299.99,
                    "currency": "USD",
                    "duration": "4h 0m",
                    "stops": 0,
                    "available_seats": 15
                },
                {
                    "id": "FL002",
                    "airline": "Delta",
                    "flight_number": "DL5678",
                    "origin": origin,
                    "destination": destination,
                    "departure_time": f"{departure_date}T14:30:00",
                    "arrival_time": f"{departure_date}T18:45:00",
                    "price": 349.99,
                    "currency": "USD",
                    "duration": "4h 15m",
                    "stops": 1,
                    "available_seats": 8
                }
            ]
            
            return {
                "flights": flights,
                "search_params": {
                    "origin": origin,
                    "destination": destination,
                    "departure_date": departure_date,
                    "return_date": return_date,
                    "passengers": passengers
                },
                "total_results": len(flights)
            }
            
        except Exception as e:
            logger.error(f"Flight search error: {e}")
            return {"error": "Failed to search flights"}
    
    async def book_flight(self, user_id: str, flight_id: str, passenger_info: Dict[str, Any]) -> Dict[str, Any]:
        """Book a flight"""
        try:
            # Generate booking confirmation
            booking_id = f"BK{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
            
            booking = {
                "booking_id": booking_id,
                "user_id": user_id,
                "type": "flight",
                "flight_id": flight_id,
                "passenger_info": passenger_info,
                "status": "confirmed",
                "booking_date": datetime.utcnow().isoformat(),
                "confirmation_code": f"CONF{booking_id[-6:]}"
            }
            
            # Store booking
            await redis_client.set(f"booking:{booking_id}", booking)
            await redis_client.lpush(f"user_bookings:{user_id}", booking_id)
            
            # Send confirmation
            await self.send_booking_confirmation(user_id, booking)
            
            return {
                "message": "Flight booked successfully",
                "booking_id": booking_id,
                "confirmation_code": booking["confirmation_code"],
                "booking": booking
            }
            
        except Exception as e:
            logger.error(f"Flight booking error: {e}")
            return {"error": "Failed to book flight"}
    
    async def search_hotels(self, location: str, check_in: str, check_out: str, 
                          guests: int = 1, rooms: int = 1) -> Dict[str, Any]:
        """Search for hotels"""
        try:
            # Simulate hotel search
            hotels = [
                {
                    "id": "HT001",
                    "name": "Grand Plaza Hotel",
                    "location": location,
                    "rating": 4.5,
                    "price_per_night": 150.00,
                    "currency": "USD",
                    "amenities": ["WiFi", "Pool", "Gym", "Restaurant"],
                    "available_rooms": 5,
                    "room_type": "Standard King",
                    "cancellation_policy": "Free cancellation until 24h before check-in"
                },
                {
                    "id": "HT002",
                    "name": "Budget Inn",
                    "location": location,
                    "rating": 3.2,
                    "price_per_night": 89.99,
                    "currency": "USD",
                    "amenities": ["WiFi", "Parking"],
                    "available_rooms": 12,
                    "room_type": "Standard Queen",
                    "cancellation_policy": "Non-refundable"
                }
            ]
            
            return {
                "hotels": hotels,
                "search_params": {
                    "location": location,
                    "check_in": check_in,
                    "check_out": check_out,
                    "guests": guests,
                    "rooms": rooms
                },
                "total_results": len(hotels)
            }
            
        except Exception as e:
            logger.error(f"Hotel search error: {e}")
            return {"error": "Failed to search hotels"}
    
    async def book_restaurant(self, user_id: str, restaurant_name: str, date: str, 
                            time: str, party_size: int, special_requests: str = "") -> Dict[str, Any]:
        """Book a restaurant reservation"""
        try:
            booking_id = f"REST{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
            
            booking = {
                "booking_id": booking_id,
                "user_id": user_id,
                "type": "restaurant",
                "restaurant_name": restaurant_name,
                "date": date,
                "time": time,
                "party_size": party_size,
                "special_requests": special_requests,
                "status": "confirmed",
                "booking_date": datetime.utcnow().isoformat()
            }
            
            await redis_client.set(f"booking:{booking_id}", booking)
            await redis_client.lpush(f"user_bookings:{user_id}", booking_id)
            
            return {
                "message": "Restaurant reservation confirmed",
                "booking_id": booking_id,
                "booking": booking
            }
            
        except Exception as e:
            logger.error(f"Restaurant booking error: {e}")
            return {"error": "Failed to book restaurant"}
    
    async def book_appointment(self, user_id: str, service_type: str, provider: str, 
                             date: str, time: str, notes: str = "") -> Dict[str, Any]:
        """Book various types of appointments (medical, beauty, professional services)"""
        try:
            booking_id = f"APPT{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
            
            booking = {
                "booking_id": booking_id,
                "user_id": user_id,
                "type": "appointment",
                "service_type": service_type,
                "provider": provider,
                "date": date,
                "time": time,
                "notes": notes,
                "status": "confirmed",
                "booking_date": datetime.utcnow().isoformat()
            }
            
            await redis_client.set(f"booking:{booking_id}", booking)
            await redis_client.lpush(f"user_bookings:{user_id}", booking_id)
            
            # Set reminder
            await self.set_appointment_reminder(user_id, booking)
            
            return {
                "message": f"{service_type} appointment booked successfully",
                "booking_id": booking_id,
                "booking": booking
            }
            
        except Exception as e:
            logger.error(f"Appointment booking error: {e}")
            return {"error": "Failed to book appointment"}
    
    async def book_transportation(self, user_id: str, transport_type: str, pickup_location: str,
                                destination: str, pickup_time: str) -> Dict[str, Any]:
        """Book transportation (Uber, Lyft, taxi, etc.)"""
        try:
            booking_id = f"TRANS{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
            
            # Estimate fare and time
            estimated_fare = self.calculate_transport_fare(pickup_location, destination, transport_type)
            estimated_time = self.calculate_travel_time(pickup_location, destination)
            
            booking = {
                "booking_id": booking_id,
                "user_id": user_id,
                "type": "transportation",
                "transport_type": transport_type,
                "pickup_location": pickup_location,
                "destination": destination,
                "pickup_time": pickup_time,
                "estimated_fare": estimated_fare,
                "estimated_time": estimated_time,
                "status": "confirmed",
                "booking_date": datetime.utcnow().isoformat()
            }
            
            await redis_client.set(f"booking:{booking_id}", booking)
            await redis_client.lpush(f"user_bookings:{user_id}", booking_id)
            
            return {
                "message": f"{transport_type} booked successfully",
                "booking_id": booking_id,
                "estimated_fare": estimated_fare,
                "estimated_time": estimated_time,
                "booking": booking
            }
            
        except Exception as e:
            logger.error(f"Transportation booking error: {e}")
            return {"error": "Failed to book transportation"}
    
    async def get_user_bookings(self, user_id: str, booking_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all bookings for a user"""
        try:
            booking_ids = await redis_client.lrange(f"user_bookings:{user_id}", 0, -1)
            bookings = []
            
            for booking_id in booking_ids:
                booking = await redis_client.get(f"booking:{booking_id}")
                if booking and (not booking_type or booking.get("type") == booking_type):
                    bookings.append(booking)
            
            return bookings
            
        except Exception as e:
            logger.error(f"Get bookings error: {e}")
            return []
    
    async def cancel_booking(self, user_id: str, booking_id: str, reason: str = "") -> Dict[str, Any]:
        """Cancel a booking"""
        try:
            booking = await redis_client.get(f"booking:{booking_id}")
            
            if not booking or booking.get("user_id") != user_id:
                return {"error": "Booking not found or unauthorized"}
            
            # Check cancellation policy
            cancellation_allowed, fee = self.check_cancellation_policy(booking)
            
            if not cancellation_allowed:
                return {"error": "Cancellation not allowed for this booking"}
            
            # Update booking status
            booking["status"] = "cancelled"
            booking["cancellation_date"] = datetime.utcnow().isoformat()
            booking["cancellation_reason"] = reason
            booking["cancellation_fee"] = fee
            
            await redis_client.set(f"booking:{booking_id}", booking)
            
            # Process refund if applicable
            refund_amount = self.calculate_refund(booking, fee)
            
            return {
                "message": "Booking cancelled successfully",
                "booking_id": booking_id,
                "cancellation_fee": fee,
                "refund_amount": refund_amount
            }
            
        except Exception as e:
            logger.error(f"Booking cancellation error: {e}")
            return {"error": "Failed to cancel booking"}
    
    async def smart_booking_suggestions(self, user_id: str, intent: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Provide intelligent booking suggestions based on user preferences and context"""
        try:
            # Get user preferences
            preferences = await self.get_user_preferences(user_id)
            
            # Analyze context and intent
            suggestions = []
            
            if "travel" in intent.lower():
                # Suggest flights and hotels based on preferences
                suggestions.extend(await self.suggest_travel_options(preferences, context))
            
            if "dinner" in intent.lower() or "restaurant" in intent.lower():
                # Suggest restaurants
                suggestions.extend(await self.suggest_restaurants(preferences, context))
            
            if "appointment" in intent.lower() or "doctor" in intent.lower():
                # Suggest appointment times
                suggestions.extend(await self.suggest_appointment_times(preferences, context))
            
            return {
                "suggestions": suggestions,
                "user_preferences": preferences,
                "context_analyzed": context
            }
            
        except Exception as e:
            logger.error(f"Smart booking suggestions error: {e}")
            return {"error": "Failed to generate suggestions"}
    
    def calculate_transport_fare(self, pickup: str, destination: str, transport_type: str) -> float:
        """Calculate estimated transportation fare"""
        # Simplified fare calculation
        base_fare = {"uber": 5.0, "lyft": 4.5, "taxi": 6.0}.get(transport_type, 5.0)
        distance_rate = {"uber": 1.5, "lyft": 1.4, "taxi": 2.0}.get(transport_type, 1.5)
        
        # Estimate distance (would use real mapping API)
        estimated_distance = 5.0  # miles
        
        return base_fare + (estimated_distance * distance_rate)
    
    def calculate_travel_time(self, pickup: str, destination: str) -> str:
        """Calculate estimated travel time"""
        # Simplified time calculation (would use real mapping API)
        return "15-20 minutes"
    
    def check_cancellation_policy(self, booking: Dict[str, Any]) -> tuple[bool, float]:
        """Check if cancellation is allowed and calculate fee"""
        booking_type = booking.get("type")
        booking_date = datetime.fromisoformat(booking.get("booking_date", ""))
        
        # Different policies for different booking types
        if booking_type == "flight":
            # 24-hour free cancellation for flights
            if datetime.utcnow() - booking_date < timedelta(hours=24):
                return True, 0.0
            else:
                return True, 50.0  # $50 cancellation fee
        
        elif booking_type == "hotel":
            # Free cancellation until 24h before check-in
            return True, 0.0
        
        elif booking_type == "restaurant":
            # Free cancellation until 2h before reservation
            return True, 0.0
        
        else:
            return True, 0.0
    
    def calculate_refund(self, booking: Dict[str, Any], cancellation_fee: float) -> float:
        """Calculate refund amount"""
        # This would integrate with payment processing
        original_amount = booking.get("total_amount", 0.0)
        return max(0, original_amount - cancellation_fee)
    
    async def send_booking_confirmation(self, user_id: str, booking: Dict[str, Any]):
        """Send booking confirmation to user"""
        try:
            # This would integrate with email/SMS service
            logger.info(f"Booking confirmation sent to user {user_id}: {booking['booking_id']}")
        except Exception as e:
            logger.error(f"Failed to send booking confirmation: {e}")
    
    async def set_appointment_reminder(self, user_id: str, booking: Dict[str, Any]):
        """Set reminder for appointment"""
        try:
            reminder_time = datetime.fromisoformat(f"{booking['date']}T{booking['time']}") - timedelta(hours=1)
            
            reminder = {
                "user_id": user_id,
                "booking_id": booking["booking_id"],
                "reminder_time": reminder_time.isoformat(),
                "message": f"Reminder: {booking['service_type']} appointment in 1 hour"
            }
            
            await redis_client.lpush(f"reminders:{user_id}", reminder)
            
        except Exception as e:
            logger.error(f"Failed to set appointment reminder: {e}")
    
    async def cleanup(self):
        """Cleanup booking service"""
        logger.info("Booking service cleanup completed")
