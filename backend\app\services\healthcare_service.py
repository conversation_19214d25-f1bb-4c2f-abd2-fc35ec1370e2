"""
Advanced Healthcare Service for JARVIS
Provides comprehensive health monitoring, medical assistance, and emergency response
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import requests
from loguru import logger

from ..core.config import settings
from ..core.redis_client import redis_client

class HealthcareService:
    """Advanced healthcare and medical assistance service"""
    
    def __init__(self):
        self.health_apis = {
            "symptoms": "https://api.infermedica.com/v3",
            "drugs": "https://api.fda.gov/drug",
            "nutrition": "https://api.edamam.com/api/nutrition-data/v2",
            "fitness": "https://api.fitbit.com/1",
            "mental_health": "https://api.headspace.com/v1"
        }
        self.emergency_contacts = {}
        self.health_profiles = {}
        
    async def initialize(self):
        """Initialize healthcare service"""
        logger.info("Healthcare service initialized")
        
    async def analyze_symptoms(self, user_id: str, symptoms: List[str], age: int, gender: str) -> Dict[str, Any]:
        """Analyze symptoms and provide medical insights"""
        try:
            # Simulate symptom analysis (would use real medical API)
            analysis = {
                "symptoms": symptoms,
                "possible_conditions": [
                    {"name": "Common Cold", "probability": 0.75, "severity": "mild"},
                    {"name": "Flu", "probability": 0.45, "severity": "moderate"}
                ],
                "recommendations": [
                    "Rest and stay hydrated",
                    "Monitor temperature",
                    "Consult doctor if symptoms worsen"
                ],
                "urgency": "low",
                "should_see_doctor": False,
                "emergency_level": 0
            }
            
            # Check for emergency symptoms
            emergency_symptoms = ["chest pain", "difficulty breathing", "severe headache", "loss of consciousness"]
            if any(symptom.lower() in emergency_symptoms for symptom in symptoms):
                analysis["urgency"] = "high"
                analysis["emergency_level"] = 9
                analysis["should_see_doctor"] = True
                await self.trigger_emergency_response(user_id, "Medical Emergency", symptoms)
            
            # Store health record
            await self.store_health_record(user_id, "symptom_analysis", analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Symptom analysis error: {e}")
            return {"error": "Failed to analyze symptoms"}
    
    async def monitor_vital_signs(self, user_id: str, vitals: Dict[str, float]) -> Dict[str, Any]:
        """Monitor and analyze vital signs"""
        try:
            # Normal ranges
            normal_ranges = {
                "heart_rate": (60, 100),
                "blood_pressure_systolic": (90, 140),
                "blood_pressure_diastolic": (60, 90),
                "temperature": (97.0, 99.5),
                "oxygen_saturation": (95, 100),
                "respiratory_rate": (12, 20)
            }
            
            alerts = []
            status = "normal"
            
            for vital, value in vitals.items():
                if vital in normal_ranges:
                    min_val, max_val = normal_ranges[vital]
                    if value < min_val or value > max_val:
                        alerts.append({
                            "vital": vital,
                            "value": value,
                            "normal_range": f"{min_val}-{max_val}",
                            "severity": "high" if value < min_val * 0.8 or value > max_val * 1.2 else "moderate"
                        })
                        status = "abnormal"
            
            # Check for critical conditions
            critical_alerts = [alert for alert in alerts if alert["severity"] == "high"]
            if critical_alerts:
                await self.trigger_emergency_response(user_id, "Critical Vital Signs", critical_alerts)
            
            result = {
                "vitals": vitals,
                "status": status,
                "alerts": alerts,
                "timestamp": datetime.utcnow().isoformat(),
                "recommendations": self.get_vital_recommendations(alerts)
            }
            
            await self.store_health_record(user_id, "vital_signs", result)
            
            return result
            
        except Exception as e:
            logger.error(f"Vital signs monitoring error: {e}")
            return {"error": "Failed to monitor vital signs"}
    
    async def medication_reminder(self, user_id: str, medication: str, dosage: str, frequency: str) -> Dict[str, Any]:
        """Set up medication reminders"""
        try:
            reminder = {
                "medication": medication,
                "dosage": dosage,
                "frequency": frequency,
                "next_dose": self.calculate_next_dose(frequency),
                "created_at": datetime.utcnow().isoformat()
            }
            
            # Store reminder
            await redis_client.lpush(f"medication_reminders:{user_id}", reminder)
            
            return {
                "message": f"Medication reminder set for {medication}",
                "next_dose": reminder["next_dose"],
                "reminder": reminder
            }
            
        except Exception as e:
            logger.error(f"Medication reminder error: {e}")
            return {"error": "Failed to set medication reminder"}
    
    async def health_assessment(self, user_id: str, assessment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive health assessment"""
        try:
            # Calculate health score
            health_score = self.calculate_health_score(assessment_data)
            
            # Generate recommendations
            recommendations = self.generate_health_recommendations(assessment_data, health_score)
            
            # Risk assessment
            risk_factors = self.assess_risk_factors(assessment_data)
            
            assessment = {
                "health_score": health_score,
                "recommendations": recommendations,
                "risk_factors": risk_factors,
                "assessment_date": datetime.utcnow().isoformat(),
                "next_assessment": (datetime.utcnow() + timedelta(days=30)).isoformat()
            }
            
            await self.store_health_record(user_id, "health_assessment", assessment)
            
            return assessment
            
        except Exception as e:
            logger.error(f"Health assessment error: {e}")
            return {"error": "Failed to complete health assessment"}
    
    async def emergency_response(self, user_id: str, emergency_type: str, location: Dict[str, float]) -> Dict[str, Any]:
        """Handle emergency situations"""
        try:
            # Get user's emergency contacts
            contacts = await self.get_emergency_contacts(user_id)
            
            # Get user's medical information
            medical_info = await self.get_medical_profile(user_id)
            
            # Determine appropriate emergency services
            services = self.determine_emergency_services(emergency_type)
            
            # Create emergency alert
            alert = {
                "user_id": user_id,
                "emergency_type": emergency_type,
                "location": location,
                "timestamp": datetime.utcnow().isoformat(),
                "medical_info": medical_info,
                "contacts_notified": [],
                "services_contacted": []
            }
            
            # Notify emergency contacts
            for contact in contacts:
                await self.notify_emergency_contact(contact, alert)
                alert["contacts_notified"].append(contact["name"])
            
            # Contact emergency services if needed
            if emergency_type in ["medical", "fire", "police"]:
                service_response = await self.contact_emergency_services(emergency_type, location, medical_info)
                alert["services_contacted"].append(service_response)
            
            # Store emergency record
            await self.store_health_record(user_id, "emergency_response", alert)
            
            return {
                "message": "Emergency response initiated",
                "alert_id": alert["timestamp"],
                "contacts_notified": len(alert["contacts_notified"]),
                "services_contacted": len(alert["services_contacted"])
            }
            
        except Exception as e:
            logger.error(f"Emergency response error: {e}")
            return {"error": "Failed to initiate emergency response"}
    
    async def mental_health_support(self, user_id: str, mood: str, stress_level: int) -> Dict[str, Any]:
        """Provide mental health support and resources"""
        try:
            # Assess mental health status
            assessment = {
                "mood": mood,
                "stress_level": stress_level,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Generate support recommendations
            if stress_level >= 8 or mood in ["depressed", "anxious", "overwhelmed"]:
                recommendations = [
                    "Consider speaking with a mental health professional",
                    "Practice deep breathing exercises",
                    "Try meditation or mindfulness",
                    "Reach out to a trusted friend or family member"
                ]
                
                # Check if crisis intervention is needed
                if stress_level >= 9 or mood == "suicidal":
                    await self.trigger_mental_health_crisis_response(user_id)
                    
            else:
                recommendations = [
                    "Maintain regular exercise",
                    "Practice gratitude",
                    "Ensure adequate sleep",
                    "Stay connected with loved ones"
                ]
            
            result = {
                "assessment": assessment,
                "recommendations": recommendations,
                "resources": [
                    {"name": "Crisis Text Line", "contact": "Text HOME to 741741"},
                    {"name": "National Suicide Prevention Lifeline", "contact": "988"},
                    {"name": "SAMHSA Helpline", "contact": "1-************"}
                ]
            }
            
            await self.store_health_record(user_id, "mental_health", result)
            
            return result
            
        except Exception as e:
            logger.error(f"Mental health support error: {e}")
            return {"error": "Failed to provide mental health support"}
    
    def calculate_health_score(self, data: Dict[str, Any]) -> float:
        """Calculate overall health score"""
        # Simplified health score calculation
        score = 100.0
        
        # Deduct points for risk factors
        if data.get("smoking", False):
            score -= 20
        if data.get("alcohol_consumption", 0) > 14:  # units per week
            score -= 10
        if data.get("exercise_frequency", 0) < 3:  # times per week
            score -= 15
        if data.get("bmi", 25) > 30:
            score -= 15
        
        return max(0, score)
    
    def generate_health_recommendations(self, data: Dict[str, Any], health_score: float) -> List[str]:
        """Generate personalized health recommendations"""
        recommendations = []
        
        if health_score < 70:
            recommendations.append("Consider consulting with a healthcare provider")
        
        if data.get("exercise_frequency", 0) < 3:
            recommendations.append("Increase physical activity to at least 3 times per week")
        
        if data.get("smoking", False):
            recommendations.append("Consider smoking cessation programs")
        
        if data.get("bmi", 25) > 25:
            recommendations.append("Maintain a healthy weight through diet and exercise")
        
        return recommendations
    
    async def store_health_record(self, user_id: str, record_type: str, data: Dict[str, Any]):
        """Store health record in database"""
        try:
            record = {
                "user_id": user_id,
                "type": record_type,
                "data": data,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await redis_client.lpush(f"health_records:{user_id}", record)
            
        except Exception as e:
            logger.error(f"Failed to store health record: {e}")
    
    async def trigger_emergency_response(self, user_id: str, emergency_type: str, details: Any):
        """Trigger emergency response protocol"""
        try:
            # This would integrate with real emergency services
            logger.warning(f"EMERGENCY: {emergency_type} for user {user_id} - {details}")
            
            # Notify emergency contacts
            # Contact emergency services
            # Send location data
            # Provide medical history
            
        except Exception as e:
            logger.error(f"Emergency response trigger error: {e}")
    
    async def cleanup(self):
        """Cleanup healthcare service"""
        logger.info("Healthcare service cleanup completed")
