version: '3.8'

services:
  # JARVIS Backend API
  jarvis-backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: jarvis-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*************************************************/jarvis_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-ultra-secure-secret-key-here
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis
    volumes:
      - ../backend:/app
      - jarvis_data:/app/data
      - jarvis_logs:/app/logs
    restart: unless-stopped
    networks:
      - jarvis-network

  # JARVIS Frontend
  jarvis-frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: jarvis-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000/ws
      - NODE_ENV=production
    depends_on:
      - jarvis-backend
    volumes:
      - ../frontend:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - jarvis-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: jarvis-postgres
    environment:
      - POSTGRES_DB=jarvis_db
      - POSTGRES_USER=jarvis
      - POSTGRES_PASSWORD=jarvis_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - jarvis-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: jarvis-redis
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - jarvis-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: jarvis-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - jarvis_logs:/var/log/nginx
    depends_on:
      - jarvis-backend
      - jarvis-frontend
    restart: unless-stopped
    networks:
      - jarvis-network

  # Elasticsearch for logging and search
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: jarvis-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    restart: unless-stopped
    networks:
      - jarvis-network

  # Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: jarvis-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - jarvis-network

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: jarvis-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped
    networks:
      - jarvis-network

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    container_name: jarvis-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - jarvis-network

  # MinIO for object storage
  minio:
    image: minio/minio:latest
    container_name: jarvis-minio
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=jarvis
      - MINIO_ROOT_PASSWORD=jarvis_minio_password
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    restart: unless-stopped
    networks:
      - jarvis-network

  # Celery Worker for background tasks
  celery-worker:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: jarvis-celery-worker
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DATABASE_URL=*************************************************/jarvis_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - postgres
      - redis
    volumes:
      - ../backend:/app
      - jarvis_data:/app/data
    restart: unless-stopped
    networks:
      - jarvis-network

  # Celery Beat for scheduled tasks
  celery-beat:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: jarvis-celery-beat
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=*************************************************/jarvis_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - postgres
      - redis
    volumes:
      - ../backend:/app
      - jarvis_data:/app/data
    restart: unless-stopped
    networks:
      - jarvis-network

  # Flower for Celery monitoring
  flower:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: jarvis-flower
    command: celery -A app.core.celery flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    ports:
      - "5555:5555"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - jarvis-network

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
  prometheus_data:
  grafana_data:
  minio_data:
  jarvis_data:
  jarvis_logs:

networks:
  jarvis-network:
    driver: bridge
