import { create } from 'zustand';
import { connect, Room, LocalAudioTrack } from 'livekit-client';
import axios from 'axios';

interface JarvisState {
  // LiveKit
  room: Room | null;
  isConnected: boolean;
  
  // Voice
  isListening: boolean;
  transcript: string;
  response: string;
  
  // AI
  conversationHistory: any[];
  currentSession: string | null;
  
  // System
  isInitialized: boolean;
  
  // Actions
  initialize: () => Promise<void>;
  connectToLiveKit: () => Promise<boolean>;
  disconnectFromLiveKit: () => void;
  startListening: () => void;
  stopListening: () => void;
  sendMessage: (message: string) => Promise<string>;
  clearConversation: () => void;
}

const API_BASE_URL = 'http://localhost:8000/api/v1';

export const useJarvisStore = create<JarvisState>((set, get) => ({
  // State
  room: null,
  isConnected: false,
  isListening: false,
  transcript: '',
  response: '',
  conversationHistory: [],
  currentSession: null,
  isInitialized: false,

  // Actions
  initialize: async () => {
    try {
      // Initialize speech recognition
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      
      if (SpeechRecognition) {
        const recognition = new SpeechRecognition();
        recognition.lang = 'en-US';
        recognition.interimResults = true;
        recognition.maxAlternatives = 1;

        recognition.onresult = (event: any) => {
          const transcript = event.results[event.results.length - 1][0].transcript;
          set({ transcript });
          
          if (event.results[event.results.length - 1].isFinal) {
            get().sendMessage(transcript);
          }
        };

        recognition.onerror = (event: any) => {
          console.error('Speech recognition error:', event.error);
          set({ isListening: false });
        };

        recognition.onend = () => {
          set({ isListening: false });
        };

        (window as any).jarvisRecognition = recognition;
      }

      set({ isInitialized: true });
    } catch (error) {
      console.error('Failed to initialize JARVIS:', error);
    }
  },

  connectToLiveKit: async () => {
    try {
      // Get LiveKit token from backend
      const response = await axios.post(`${API_BASE_URL}/livekit/create-room`);
      const { room_url, user_token } = response.data;

      const room = await connect(room_url, user_token, {
        audio: true,
        video: false,
      });

      set({ room, isConnected: true });

      // Publish local audio track
      const audioTrack = await LocalAudioTrack.create();
      await room.localParticipant.publishTrack(audioTrack);

      room.on('disconnected', () => {
        set({ isConnected: false, room: null });
      });

      return true;
    } catch (error) {
      console.error('Failed to connect to LiveKit:', error);
      return false;
    }
  },

  disconnectFromLiveKit: () => {
    const { room } = get();
    room?.disconnect();
    set({ room: null, isConnected: false });
  },

  startListening: () => {
    const recognition = (window as any).jarvisRecognition;
    if (recognition && !get().isListening) {
      recognition.start();
      set({ isListening: true });
    }
  },

  stopListening: () => {
    const recognition = (window as any).jarvisRecognition;
    if (recognition && get().isListening) {
      recognition.stop();
      set({ isListening: false });
    }
  },

  sendMessage: async (message: string) => {
    try {
      const { currentSession, conversationHistory } = get();
      
      const response = await axios.post(`${API_BASE_URL}/ai/chat`, {
        message,
        session_id: currentSession,
        context: {}
      });

      const { response: aiResponse, session_id } = response.data;

      // Update conversation history
      const newHistory = [
        ...conversationHistory,
        { type: 'user', message, timestamp: new Date() },
        { type: 'assistant', message: aiResponse, timestamp: new Date() }
      ];

      set({
        response: aiResponse,
        conversationHistory: newHistory,
        currentSession: session_id
      });

      // Speak the response
      if (window.speechSynthesis) {
        const utterance = new SpeechSynthesisUtterance(aiResponse);
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        window.speechSynthesis.speak(utterance);
      }

      return aiResponse;
    } catch (error) {
      console.error('Failed to send message:', error);
      const errorMessage = 'Sorry, I encountered an error processing your request.';
      set({ response: errorMessage });
      return errorMessage;
    }
  },

  clearConversation: () => {
    set({
      conversationHistory: [],
      currentSession: null,
      transcript: '',
      response: ''
    });
  }
}));
