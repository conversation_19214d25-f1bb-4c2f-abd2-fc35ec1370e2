#!/bin/bash

# JARVIS Ultra-Advanced AI Assistant Deployment Script
# This script deploys JARVIS to various environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-"development"}
DEPLOYMENT_TYPE=${2:-"docker"}
VERSION=${3:-"latest"}

echo -e "${BLUE}🚀 JARVIS Ultra-Advanced AI Assistant Deployment${NC}"
echo -e "${BLUE}=================================================${NC}"
echo -e "Environment: ${GREEN}$ENVIRONMENT${NC}"
echo -e "Deployment Type: ${GREEN}$DEPLOYMENT_TYPE${NC}"
echo -e "Version: ${GREEN}$VERSION${NC}"
echo ""

# Function to check prerequisites
check_prerequisites() {
    echo -e "${YELLOW}📋 Checking prerequisites...${NC}"
    
    if [ "$DEPLOYMENT_TYPE" = "docker" ]; then
        if ! command -v docker &> /dev/null; then
            echo -e "${RED}❌ Docker is not installed${NC}"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            echo -e "${RED}❌ Docker Compose is not installed${NC}"
            exit 1
        fi
    elif [ "$DEPLOYMENT_TYPE" = "kubernetes" ]; then
        if ! command -v kubectl &> /dev/null; then
            echo -e "${RED}❌ kubectl is not installed${NC}"
            exit 1
        fi
        
        if ! command -v helm &> /dev/null; then
            echo -e "${RED}❌ Helm is not installed${NC}"
            exit 1
        fi
    fi
    
    echo -e "${GREEN}✅ Prerequisites check passed${NC}"
}

# Function to build Docker images
build_images() {
    echo -e "${YELLOW}🔨 Building Docker images...${NC}"
    
    # Build backend image
    echo -e "Building backend image..."
    docker build -t jarvis/backend:$VERSION ../backend/
    
    # Build frontend image
    echo -e "Building frontend image..."
    docker build -t jarvis/frontend:$VERSION ../frontend/
    
    echo -e "${GREEN}✅ Docker images built successfully${NC}"
}

# Function to deploy with Docker Compose
deploy_docker() {
    echo -e "${YELLOW}🐳 Deploying with Docker Compose...${NC}"
    
    # Create environment file
    cat > .env << EOF
ENVIRONMENT=$ENVIRONMENT
VERSION=$VERSION
OPENAI_API_KEY=${OPENAI_API_KEY:-""}
ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-""}
GOOGLE_API_KEY=${GOOGLE_API_KEY:-""}
EOF
    
    # Deploy services
    docker-compose -f docker-compose.yml up -d
    
    echo -e "${GREEN}✅ Docker deployment completed${NC}"
    echo -e "${BLUE}📊 Services Status:${NC}"
    docker-compose ps
}

# Function to deploy to Kubernetes
deploy_kubernetes() {
    echo -e "${YELLOW}☸️  Deploying to Kubernetes...${NC}"
    
    # Apply namespace and configs
    kubectl apply -f kubernetes/namespace.yaml
    
    # Apply database deployments
    kubectl apply -f kubernetes/database-deployment.yaml
    
    # Wait for database to be ready
    echo -e "Waiting for database to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres -n jarvis --timeout=300s
    kubectl wait --for=condition=ready pod -l app=redis -n jarvis --timeout=300s
    
    # Apply backend deployment
    kubectl apply -f kubernetes/backend-deployment.yaml
    
    # Wait for backend to be ready
    echo -e "Waiting for backend to be ready..."
    kubectl wait --for=condition=ready pod -l app=jarvis-backend -n jarvis --timeout=300s
    
    # Apply frontend deployment
    kubectl apply -f kubernetes/frontend-deployment.yaml
    
    # Wait for frontend to be ready
    echo -e "Waiting for frontend to be ready..."
    kubectl wait --for=condition=ready pod -l app=jarvis-frontend -n jarvis --timeout=300s
    
    # Apply ingress
    kubectl apply -f kubernetes/ingress.yaml
    
    echo -e "${GREEN}✅ Kubernetes deployment completed${NC}"
    echo -e "${BLUE}📊 Pods Status:${NC}"
    kubectl get pods -n jarvis
}

# Function to run health checks
health_check() {
    echo -e "${YELLOW}🏥 Running health checks...${NC}"
    
    if [ "$DEPLOYMENT_TYPE" = "docker" ]; then
        # Check Docker services
        echo -e "Checking backend health..."
        for i in {1..30}; do
            if curl -f http://localhost:8000/health &> /dev/null; then
                echo -e "${GREEN}✅ Backend is healthy${NC}"
                break
            fi
            echo -e "Waiting for backend... ($i/30)"
            sleep 10
        done
        
        echo -e "Checking frontend health..."
        for i in {1..30}; do
            if curl -f http://localhost:3000 &> /dev/null; then
                echo -e "${GREEN}✅ Frontend is healthy${NC}"
                break
            fi
            echo -e "Waiting for frontend... ($i/30)"
            sleep 10
        done
        
    elif [ "$DEPLOYMENT_TYPE" = "kubernetes" ]; then
        # Check Kubernetes services
        kubectl get pods -n jarvis
        kubectl get services -n jarvis
        kubectl get ingress -n jarvis
    fi
}

# Function to display access information
display_access_info() {
    echo -e "${BLUE}🌐 Access Information${NC}"
    echo -e "${BLUE}===================${NC}"
    
    if [ "$DEPLOYMENT_TYPE" = "docker" ]; then
        echo -e "Frontend: ${GREEN}http://localhost:3000${NC}"
        echo -e "Backend API: ${GREEN}http://localhost:8000${NC}"
        echo -e "API Documentation: ${GREEN}http://localhost:8000/docs${NC}"
        echo -e "Grafana: ${GREEN}http://localhost:3001${NC} (admin/admin)"
        echo -e "Kibana: ${GREEN}http://localhost:5601${NC}"
        echo -e "Flower: ${GREEN}http://localhost:5555${NC}"
        echo -e "MinIO: ${GREEN}http://localhost:9001${NC} (jarvis/jarvis_minio_password)"
        
    elif [ "$DEPLOYMENT_TYPE" = "kubernetes" ]; then
        echo -e "Frontend: ${GREEN}https://jarvis.ai${NC}"
        echo -e "Backend API: ${GREEN}https://api.jarvis.ai${NC}"
        echo -e "API Documentation: ${GREEN}https://api.jarvis.ai/docs${NC}"
        
        # Get ingress IP
        INGRESS_IP=$(kubectl get ingress jarvis-ingress -n jarvis -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        if [ -n "$INGRESS_IP" ]; then
            echo -e "Ingress IP: ${GREEN}$INGRESS_IP${NC}"
        fi
    fi
}

# Function to setup monitoring
setup_monitoring() {
    echo -e "${YELLOW}📊 Setting up monitoring...${NC}"
    
    if [ "$DEPLOYMENT_TYPE" = "docker" ]; then
        echo -e "Monitoring services are included in docker-compose.yml"
        echo -e "Prometheus: http://localhost:9090"
        echo -e "Grafana: http://localhost:3001"
        
    elif [ "$DEPLOYMENT_TYPE" = "kubernetes" ]; then
        # Install monitoring stack with Helm
        helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
        helm repo add grafana https://grafana.github.io/helm-charts
        helm repo update
        
        # Install Prometheus
        helm install prometheus prometheus-community/kube-prometheus-stack \
            --namespace jarvis \
            --set grafana.adminPassword=admin
        
        echo -e "${GREEN}✅ Monitoring stack installed${NC}"
    fi
}

# Main deployment flow
main() {
    echo -e "${BLUE}Starting JARVIS deployment...${NC}"
    
    check_prerequisites
    
    if [ "$DEPLOYMENT_TYPE" = "docker" ]; then
        build_images
        deploy_docker
    elif [ "$DEPLOYMENT_TYPE" = "kubernetes" ]; then
        build_images
        deploy_kubernetes
        setup_monitoring
    else
        echo -e "${RED}❌ Invalid deployment type: $DEPLOYMENT_TYPE${NC}"
        echo -e "Valid options: docker, kubernetes"
        exit 1
    fi
    
    health_check
    display_access_info
    
    echo -e "${GREEN}🎉 JARVIS deployment completed successfully!${NC}"
    echo -e "${BLUE}📚 Check the documentation for usage instructions${NC}"
}

# Run main function
main
