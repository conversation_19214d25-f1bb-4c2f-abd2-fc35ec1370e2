"""File management router"""

from fastapi import APIRouter, Depends, UploadFile, File
from ..core.database import User
from ..core.security import get_current_active_user

router = APIRouter()

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    return {"filename": file.filename, "status": "uploaded"}

@router.get("/list")
async def list_files(current_user: User = Depends(get_current_active_user)):
    return {"files": []}
