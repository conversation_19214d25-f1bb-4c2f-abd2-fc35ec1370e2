"""Web scraping and information retrieval router"""

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from ..core.database import User
from ..core.security import get_current_active_user

router = APIRouter()

class SearchRequest(BaseModel):
    query: str
    limit: int = 10

@router.post("/search")
async def web_search(
    request: SearchRequest,
    current_user: User = Depends(get_current_active_user)
):
    return {"results": [], "query": request.query}

@router.post("/scrape")
async def scrape_website(
    url: str,
    current_user: User = Depends(get_current_active_user)
):
    return {"content": "", "url": url}
