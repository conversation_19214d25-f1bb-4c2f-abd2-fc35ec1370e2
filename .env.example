# JARVIS Environment Configuration

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here

# LiveKit Configuration
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret
LIVEKIT_URL=wss://your-livekit-server.com

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key

# External APIs
WEATHER_API_KEY=your-openweathermap-api-key
GOOGLE_API_KEY=your-google-api-key
GOOGLE_CSE_ID=your-google-custom-search-engine-id
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
STABILITY_API_KEY=your-stability-ai-api-key

# Email Configuration
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM=<EMAIL>

# Social Media APIs
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
TWITTER_ACCESS_TOKEN=your-twitter-access-token
TWITTER_ACCESS_TOKEN_SECRET=your-twitter-access-token-secret

# Cloud Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_BUCKET_NAME=your-s3-bucket

# Home Assistant
HOME_ASSISTANT_URL=http://localhost:8123
HOME_ASSISTANT_TOKEN=your-home-assistant-token

# Healthcare APIs
INFERMEDICA_APP_ID=your-infermedica-app-id
INFERMEDICA_APP_KEY=your-infermedica-app-key

# Booking APIs
AMADEUS_API_KEY=your-amadeus-api-key
AMADEUS_API_SECRET=your-amadeus-api-secret
BOOKING_COM_API_KEY=your-booking-com-api-key
OPENTABLE_API_KEY=your-opentable-api-key

# Transportation APIs
UBER_CLIENT_ID=your-uber-client-id
UBER_CLIENT_SECRET=your-uber-client-secret
LYFT_CLIENT_ID=your-lyft-client-id
LYFT_CLIENT_SECRET=your-lyft-client-secret

# Financial APIs
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-api-key
PLAID_CLIENT_ID=your-plaid-client-id
PLAID_SECRET=your-plaid-secret

# News APIs
NEWS_API_KEY=your-news-api-key

# Security
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Development
DEBUG=True
LOG_LEVEL=INFO

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
