"""
Advanced NLP router for JARVIS
Ultra-advanced natural language processing and understanding
"""

from typing import Dict, List, Any, Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from pydantic import BaseModel

from ..core.database import User
from ..core.security import get_current_active_user
from ..services.advanced_nlp_service import AdvancedNLPService

router = APIRouter()

# Global NLP service instance
nlp_service = AdvancedNLPService()

class TextAnalysisRequest(BaseModel):
    text: str
    analysis_type: str = "full"
    language: Optional[str] = None

class SentimentAnalysisRequest(BaseModel):
    text: str
    include_emotions: bool = True

class IntentClassificationRequest(BaseModel):
    text: str
    context: Optional[Dict[str, Any]] = None

class TextSummarizationRequest(BaseModel):
    text: str
    max_length: int = 150
    min_length: int = 30

class QuestionAnsweringRequest(BaseModel):
    question: str
    context: str

class TextGenerationRequest(BaseModel):
    prompt: str
    max_length: int = 100
    temperature: float = 0.7

@router.on_event("startup")
async def startup_nlp_service():
    """Initialize NLP service on startup"""
    await nlp_service.initialize()

@router.post("/analyze-comprehensive")
async def analyze_text_comprehensive(
    request: TextAnalysisRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Comprehensive text analysis with all NLP models"""
    
    try:
        analysis = await nlp_service.analyze_text_comprehensive(
            text=request.text,
            analysis_type=request.analysis_type
        )
        
        return {
            "text": request.text,
            "analysis_type": request.analysis_type,
            "analysis": analysis,
            "user_id": current_user.id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Text analysis failed: {str(e)}")

@router.post("/detect-language")
async def detect_text_language(
    text: str,
    current_user: User = Depends(get_current_active_user)
):
    """Detect language of the text"""
    
    try:
        language_data = await nlp_service.detect_language(text)
        
        return {
            "text": text,
            "language_detection": language_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Language detection failed: {str(e)}")

@router.post("/analyze-sentiment")
async def analyze_sentiment_advanced(
    request: SentimentAnalysisRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Advanced sentiment analysis with multiple models"""
    
    try:
        sentiment_data = await nlp_service.analyze_sentiment_advanced(request.text)
        
        if request.include_emotions:
            emotion_data = await nlp_service.analyze_emotions(request.text)
            sentiment_data["emotions"] = emotion_data
        
        return {
            "text": request.text,
            "sentiment_analysis": sentiment_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Sentiment analysis failed: {str(e)}")

@router.post("/classify-intent")
async def classify_intent_advanced(
    request: IntentClassificationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Advanced intent classification with context understanding"""
    
    try:
        intent_data = await nlp_service.classify_intent_advanced(request.text)
        
        return {
            "text": request.text,
            "context": request.context,
            "intent_classification": intent_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Intent classification failed: {str(e)}")

@router.post("/extract-entities")
async def extract_entities_advanced(
    text: str,
    current_user: User = Depends(get_current_active_user)
):
    """Advanced named entity recognition"""
    
    try:
        entities_data = await nlp_service.extract_entities_advanced(text)
        
        return {
            "text": text,
            "entities": entities_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Entity extraction failed: {str(e)}")

@router.post("/extract-topics")
async def extract_topics_and_phrases(
    text: str,
    current_user: User = Depends(get_current_active_user)
):
    """Extract key topics and phrases from text"""
    
    try:
        topics_data = await nlp_service.extract_topics_and_phrases(text)
        
        return {
            "text": text,
            "topics_and_phrases": topics_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Topic extraction failed: {str(e)}")

@router.post("/analyze-complexity")
async def analyze_text_complexity(
    text: str,
    current_user: User = Depends(get_current_active_user)
):
    """Analyze text complexity and readability"""
    
    try:
        complexity_data = await nlp_service.analyze_text_complexity(text)
        
        return {
            "text": text,
            "complexity_analysis": complexity_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Complexity analysis failed: {str(e)}")

@router.post("/generate-embeddings")
async def generate_text_embeddings(
    text: str,
    current_user: User = Depends(get_current_active_user)
):
    """Generate semantic embeddings for text"""
    
    try:
        embeddings_data = await nlp_service.generate_embeddings(text)
        
        return {
            "text": text,
            "embeddings": embeddings_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Embeddings generation failed: {str(e)}")

@router.post("/analyze-grammar")
async def analyze_grammar_and_style(
    text: str,
    current_user: User = Depends(get_current_active_user)
):
    """Analyze grammar and writing style"""
    
    try:
        grammar_data = await nlp_service.analyze_grammar_and_style(text)
        
        return {
            "text": text,
            "grammar_analysis": grammar_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Grammar analysis failed: {str(e)}")

@router.post("/summarize")
async def summarize_text(
    request: TextSummarizationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Generate text summary"""
    
    try:
        summary_data = await nlp_service.summarize_text(
            text=request.text,
            max_length=request.max_length
        )
        
        return {
            "original_text": request.text,
            "summary": summary_data,
            "parameters": {
                "max_length": request.max_length,
                "min_length": request.min_length
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Text summarization failed: {str(e)}")

@router.post("/answer-question")
async def answer_question(
    request: QuestionAnsweringRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Answer questions based on context"""
    
    try:
        answer_data = await nlp_service.answer_question(
            question=request.question,
            context=request.context
        )
        
        return {
            "question": request.question,
            "context": request.context,
            "answer": answer_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Question answering failed: {str(e)}")

@router.post("/generate-text")
async def generate_text(
    request: TextGenerationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Generate text based on prompt"""
    
    try:
        generated_data = await nlp_service.generate_text(
            prompt=request.prompt,
            max_length=request.max_length
        )
        
        return {
            "prompt": request.prompt,
            "generated": generated_data,
            "parameters": {
                "max_length": request.max_length,
                "temperature": request.temperature
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Text generation failed: {str(e)}")

@router.post("/compare-similarity")
async def compare_text_similarity(
    text1: str,
    text2: str,
    current_user: User = Depends(get_current_active_user)
):
    """Compare semantic similarity between two texts"""
    
    try:
        # Generate embeddings for both texts
        embeddings1 = await nlp_service.generate_embeddings(text1)
        embeddings2 = await nlp_service.generate_embeddings(text2)
        
        # Calculate cosine similarity
        import numpy as np
        from sklearn.metrics.pairwise import cosine_similarity
        
        if embeddings1.get("embeddings") and embeddings2.get("embeddings"):
            emb1 = np.array(embeddings1["embeddings"]).reshape(1, -1)
            emb2 = np.array(embeddings2["embeddings"]).reshape(1, -1)
            similarity = cosine_similarity(emb1, emb2)[0][0]
        else:
            similarity = 0.0
        
        return {
            "text1": text1,
            "text2": text2,
            "similarity_score": float(similarity),
            "similarity_level": "high" if similarity > 0.8 else "medium" if similarity > 0.5 else "low"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Similarity comparison failed: {str(e)}")

@router.get("/nlp-capabilities")
async def get_nlp_capabilities():
    """Get available NLP capabilities"""
    
    return {
        "capabilities": [
            "language_detection",
            "sentiment_analysis",
            "emotion_analysis",
            "intent_classification",
            "named_entity_recognition",
            "topic_extraction",
            "text_complexity_analysis",
            "semantic_embeddings",
            "grammar_analysis",
            "text_summarization",
            "question_answering",
            "text_generation",
            "similarity_comparison"
        ],
        "models": [
            "spaCy",
            "Transformers (BERT, RoBERTa, BART)",
            "Sentence Transformers",
            "TextBlob",
            "NLTK",
            "GPT-2"
        ],
        "supported_languages": [
            "English", "Spanish", "French", "German", "Italian", 
            "Portuguese", "Russian", "Japanese", "Korean", "Chinese"
        ],
        "analysis_types": [
            "full", "sentiment", "entities", "topics", "complexity", 
            "grammar", "embeddings", "intent", "language"
        ]
    }

@router.get("/nlp-status")
async def get_nlp_service_status():
    """Get NLP service status"""
    
    return {
        "status": "operational",
        "models_loaded": True,
        "gpu_acceleration": False,  # Would check actual GPU availability
        "real_time_processing": True,
        "batch_processing": True,
        "max_text_length": 10000,
        "api_version": "2.0",
        "features": {
            "multilingual": True,
            "real_time_analysis": True,
            "custom_models": True,
            "fine_tuning": False
        }
    }
