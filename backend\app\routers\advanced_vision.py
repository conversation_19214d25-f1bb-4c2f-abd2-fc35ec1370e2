"""
Advanced Computer Vision router for JARVIS
Ultra-advanced object detection, scene understanding, and visual AI
"""

from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from pydantic import BaseModel

from ..core.database import User
from ..core.security import get_current_active_user
from ..services.advanced_vision_service import AdvancedVisionService

router = APIRouter()

# Global vision service instance
vision_service = AdvancedVisionService()

class ImageAnalysisRequest(BaseModel):
    analysis_type: str = "full"
    include_objects: bool = True
    include_faces: bool = True
    include_text: bool = True
    include_scene: bool = True
    include_pose: bool = True

class ImageGenerationRequest(BaseModel):
    prompt: str
    style: str = "realistic"
    width: int = 512
    height: int = 512

@router.on_event("startup")
async def startup_vision_service():
    """Initialize vision service on startup"""
    await vision_service.initialize()

@router.post("/analyze-comprehensive")
async def analyze_image_comprehensive(
    image_file: UploadFile = File(...),
    analysis_type: str = Form("full"),
    current_user: User = Depends(get_current_active_user)
):
    """Comprehensive image analysis with all AI models"""
    
    try:
        # Read image data
        image_data = await image_file.read()
        
        # Perform comprehensive analysis
        analysis = await vision_service.analyze_image_comprehensive(
            image_data=image_data,
            analysis_type=analysis_type
        )
        
        return {
            "filename": image_file.filename,
            "analysis": analysis,
            "user_id": current_user.id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")

@router.post("/detect-objects")
async def detect_objects_advanced(
    image_file: UploadFile = File(...),
    confidence_threshold: float = Form(0.5),
    current_user: User = Depends(get_current_active_user)
):
    """Advanced object detection using YOLO"""
    
    try:
        image_data = await image_file.read()
        
        # Convert to OpenCV format and detect objects
        import cv2
        import numpy as np
        from PIL import Image
        from io import BytesIO
        
        image = Image.open(BytesIO(image_data))
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        objects = await vision_service.detect_objects_yolo(cv_image)
        
        # Filter by confidence threshold
        filtered_objects = [obj for obj in objects if obj.get("confidence", 0) >= confidence_threshold]
        
        return {
            "filename": image_file.filename,
            "objects_detected": len(filtered_objects),
            "objects": filtered_objects,
            "confidence_threshold": confidence_threshold
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Object detection failed: {str(e)}")

@router.post("/analyze-faces")
async def analyze_faces_advanced(
    image_file: UploadFile = File(...),
    include_emotions: bool = Form(True),
    include_age_gender: bool = Form(True),
    current_user: User = Depends(get_current_active_user)
):
    """Advanced face analysis with emotions and demographics"""
    
    try:
        image_data = await image_file.read()
        
        import cv2
        import numpy as np
        from PIL import Image
        from io import BytesIO
        
        image = Image.open(BytesIO(image_data))
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        faces = await vision_service.detect_faces_advanced(cv_image)
        
        return {
            "filename": image_file.filename,
            "faces_detected": len(faces),
            "faces": faces,
            "analysis_options": {
                "emotions": include_emotions,
                "age_gender": include_age_gender
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Face analysis failed: {str(e)}")

@router.post("/extract-text")
async def extract_text_advanced(
    image_file: UploadFile = File(...),
    languages: str = Form("en"),
    current_user: User = Depends(get_current_active_user)
):
    """Advanced OCR text extraction"""
    
    try:
        image_data = await image_file.read()
        
        from PIL import Image
        from io import BytesIO
        
        image = Image.open(BytesIO(image_data))
        
        text_data = await vision_service.extract_text_advanced(image)
        
        return {
            "filename": image_file.filename,
            "text_extracted": text_data,
            "languages": languages.split(",")
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Text extraction failed: {str(e)}")

@router.post("/understand-scene")
async def understand_scene_context(
    image_file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Advanced scene understanding and context analysis"""
    
    try:
        image_data = await image_file.read()
        
        from PIL import Image
        from io import BytesIO
        
        image = Image.open(BytesIO(image_data))
        
        scene_data = await vision_service.understand_scene(image)
        
        return {
            "filename": image_file.filename,
            "scene_analysis": scene_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Scene understanding failed: {str(e)}")

@router.post("/generate-caption")
async def generate_image_caption(
    image_file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Generate natural language description of image"""
    
    try:
        image_data = await image_file.read()
        
        from PIL import Image
        from io import BytesIO
        
        image = Image.open(BytesIO(image_data))
        
        caption_data = await vision_service.generate_image_caption(image)
        
        return {
            "filename": image_file.filename,
            "caption": caption_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Caption generation failed: {str(e)}")

@router.post("/analyze-colors")
async def analyze_image_colors(
    image_file: UploadFile = File(...),
    num_colors: int = Form(5),
    current_user: User = Depends(get_current_active_user)
):
    """Analyze color distribution and dominant colors"""
    
    try:
        image_data = await image_file.read()
        
        from PIL import Image
        from io import BytesIO
        
        image = Image.open(BytesIO(image_data))
        
        color_analysis = await vision_service.analyze_colors(image)
        
        return {
            "filename": image_file.filename,
            "color_analysis": color_analysis,
            "num_colors_requested": num_colors
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Color analysis failed: {str(e)}")

@router.post("/assess-quality")
async def assess_image_quality(
    image_file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Assess image quality metrics"""
    
    try:
        image_data = await image_file.read()
        
        import cv2
        import numpy as np
        from PIL import Image
        from io import BytesIO
        
        image = Image.open(BytesIO(image_data))
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        quality_metrics = await vision_service.assess_image_quality(cv_image)
        
        return {
            "filename": image_file.filename,
            "quality_assessment": quality_metrics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Quality assessment failed: {str(e)}")

@router.post("/detect-pose")
async def detect_pose_and_hands(
    image_file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Detect human pose and hand gestures"""
    
    try:
        image_data = await image_file.read()
        
        import cv2
        import numpy as np
        from PIL import Image
        from io import BytesIO
        
        image = Image.open(BytesIO(image_data))
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        pose_data = await vision_service.detect_pose_and_hands(cv_image)
        
        return {
            "filename": image_file.filename,
            "pose_detection": pose_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Pose detection failed: {str(e)}")

@router.post("/generate-image")
async def generate_image_from_text(
    request: ImageGenerationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Generate image from text description"""
    
    try:
        image_bytes = await vision_service.generate_image_from_text(
            prompt=request.prompt,
            style=request.style
        )
        
        if not image_bytes:
            raise HTTPException(status_code=500, detail="Image generation failed")
        
        import base64
        image_b64 = base64.b64encode(image_bytes).decode()
        
        return {
            "prompt": request.prompt,
            "style": request.style,
            "image_data": image_b64,
            "format": "png"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Image generation failed: {str(e)}")

@router.get("/vision-capabilities")
async def get_vision_capabilities():
    """Get available computer vision capabilities"""
    
    return {
        "capabilities": [
            "object_detection",
            "face_recognition",
            "emotion_detection",
            "age_gender_estimation",
            "text_extraction_ocr",
            "scene_understanding",
            "image_captioning",
            "color_analysis",
            "quality_assessment",
            "pose_detection",
            "hand_gesture_recognition",
            "image_generation"
        ],
        "models": [
            "YOLO v8",
            "MediaPipe",
            "EasyOCR",
            "BLIP Image Captioning",
            "Stable Diffusion",
            "Face Recognition",
            "OpenCV"
        ],
        "supported_formats": ["jpg", "jpeg", "png", "bmp", "tiff", "webp"],
        "max_image_size": "10MB",
        "processing_time": "1-10 seconds depending on complexity"
    }

@router.get("/vision-status")
async def get_vision_service_status():
    """Get computer vision service status"""
    
    return {
        "status": "operational",
        "models_loaded": True,
        "gpu_acceleration": False,  # Would check actual GPU availability
        "supported_languages": ["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"],
        "real_time_processing": True,
        "batch_processing": True,
        "api_version": "2.0"
    }
