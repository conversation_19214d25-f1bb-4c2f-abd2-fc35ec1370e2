"""
Advanced Booking router for JARVIS
Handles all types of bookings with zero human intervention
"""

from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel
from datetime import datetime

from ..core.database import User
from ..core.security import get_current_active_user
from ..services.booking_service import BookingService

router = APIRouter()

# Global booking service instance
booking_service = BookingService()

class FlightSearchRequest(BaseModel):
    origin: str
    destination: str
    departure_date: str
    return_date: Optional[str] = None
    passengers: int = 1
    class_preference: Optional[str] = "economy"

class FlightBookingRequest(BaseModel):
    flight_id: str
    passenger_info: Dict[str, Any]
    payment_method: str
    special_requests: Optional[str] = ""

class HotelSearchRequest(BaseModel):
    location: str
    check_in: str
    check_out: str
    guests: int = 1
    rooms: int = 1
    price_range: Optional[Dict[str, float]] = None

class RestaurantBookingRequest(BaseModel):
    restaurant_name: str
    date: str
    time: str
    party_size: int
    special_requests: Optional[str] = ""
    cuisine_preference: Optional[str] = ""

class AppointmentBookingRequest(BaseModel):
    service_type: str
    provider: str
    date: str
    time: str
    notes: Optional[str] = ""
    urgency: Optional[str] = "normal"

class TransportationBookingRequest(BaseModel):
    transport_type: str
    pickup_location: str
    destination: str
    pickup_time: str
    vehicle_preference: Optional[str] = ""

class SmartBookingRequest(BaseModel):
    intent: str
    context: Dict[str, Any]
    preferences: Optional[Dict[str, Any]] = None

@router.on_event("startup")
async def startup_booking_service():
    """Initialize booking service on startup"""
    await booking_service.initialize()

@router.post("/search-flights")
async def search_flights(
    request: FlightSearchRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Search for flights based on criteria"""
    
    try:
        results = await booking_service.search_flights(
            origin=request.origin,
            destination=request.destination,
            departure_date=request.departure_date,
            return_date=request.return_date,
            passengers=request.passengers
        )
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Flight search failed: {str(e)}")

@router.post("/book-flight")
async def book_flight(
    request: FlightBookingRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Book a flight"""
    
    try:
        booking = await booking_service.book_flight(
            user_id=str(current_user.id),
            flight_id=request.flight_id,
            passenger_info=request.passenger_info
        )
        
        # Send confirmation email in background
        background_tasks.add_task(
            booking_service.send_booking_confirmation,
            str(current_user.id),
            booking
        )
        
        return booking
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Flight booking failed: {str(e)}")

@router.post("/search-hotels")
async def search_hotels(
    request: HotelSearchRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Search for hotels"""
    
    try:
        results = await booking_service.search_hotels(
            location=request.location,
            check_in=request.check_in,
            check_out=request.check_out,
            guests=request.guests,
            rooms=request.rooms
        )
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Hotel search failed: {str(e)}")

@router.post("/book-restaurant")
async def book_restaurant(
    request: RestaurantBookingRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Book a restaurant reservation"""
    
    try:
        booking = await booking_service.book_restaurant(
            user_id=str(current_user.id),
            restaurant_name=request.restaurant_name,
            date=request.date,
            time=request.time,
            party_size=request.party_size,
            special_requests=request.special_requests
        )
        
        # Set reminder in background
        background_tasks.add_task(
            booking_service.set_appointment_reminder,
            str(current_user.id),
            booking
        )
        
        return booking
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Restaurant booking failed: {str(e)}")

@router.post("/book-appointment")
async def book_appointment(
    request: AppointmentBookingRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Book various types of appointments"""
    
    try:
        booking = await booking_service.book_appointment(
            user_id=str(current_user.id),
            service_type=request.service_type,
            provider=request.provider,
            date=request.date,
            time=request.time,
            notes=request.notes
        )
        
        # Set reminder
        background_tasks.add_task(
            booking_service.set_appointment_reminder,
            str(current_user.id),
            booking
        )
        
        return booking
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Appointment booking failed: {str(e)}")

@router.post("/book-transportation")
async def book_transportation(
    request: TransportationBookingRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Book transportation (Uber, Lyft, taxi, etc.)"""
    
    try:
        booking = await booking_service.book_transportation(
            user_id=str(current_user.id),
            transport_type=request.transport_type,
            pickup_location=request.pickup_location,
            destination=request.destination,
            pickup_time=request.pickup_time
        )
        
        return booking
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Transportation booking failed: {str(e)}")

@router.get("/bookings")
async def get_user_bookings(
    booking_type: Optional[str] = None,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    """Get all bookings for the user"""
    
    try:
        bookings = await booking_service.get_user_bookings(
            user_id=str(current_user.id),
            booking_type=booking_type
        )
        
        # Filter by status if provided
        if status:
            bookings = [b for b in bookings if b.get("status") == status]
        
        return {
            "bookings": bookings,
            "total": len(bookings),
            "filters": {
                "booking_type": booking_type,
                "status": status
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get bookings: {str(e)}")

@router.post("/cancel-booking/{booking_id}")
async def cancel_booking(
    booking_id: str,
    reason: Optional[str] = "",
    current_user: User = Depends(get_current_active_user)
):
    """Cancel a booking"""
    
    try:
        result = await booking_service.cancel_booking(
            user_id=str(current_user.id),
            booking_id=booking_id,
            reason=reason
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Booking cancellation failed: {str(e)}")

@router.post("/smart-booking")
async def smart_booking_suggestions(
    request: SmartBookingRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Get intelligent booking suggestions based on user intent and context"""
    
    try:
        suggestions = await booking_service.smart_booking_suggestions(
            user_id=str(current_user.id),
            intent=request.intent,
            context=request.context
        )
        
        return suggestions
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Smart booking suggestions failed: {str(e)}")

@router.get("/booking-history")
async def get_booking_history(
    days: int = 30,
    current_user: User = Depends(get_current_active_user)
):
    """Get booking history and analytics"""
    
    try:
        # This would analyze booking patterns and provide insights
        history = {
            "total_bookings": 0,
            "booking_types": {},
            "spending_analysis": {
                "total_spent": 0.0,
                "average_per_booking": 0.0,
                "most_expensive_category": ""
            },
            "patterns": {
                "most_booked_day": "",
                "most_booked_time": "",
                "favorite_destinations": []
            },
            "upcoming_bookings": [],
            "recommendations": []
        }
        
        return history
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get booking history: {str(e)}")

@router.post("/auto-book")
async def auto_book_based_on_preferences(
    intent: str,
    context: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Automatically book based on user preferences and past behavior"""
    
    try:
        # This would use AI to automatically make bookings based on user patterns
        # For safety, this would require explicit user confirmation for high-value bookings
        
        auto_booking_result = {
            "message": "Auto-booking analysis completed",
            "suggestions": [
                {
                    "type": "restaurant",
                    "confidence": 0.85,
                    "details": "Based on your preferences, I suggest booking at your favorite Italian restaurant",
                    "auto_bookable": True
                }
            ],
            "requires_confirmation": True,
            "estimated_cost": 0.0
        }
        
        return auto_booking_result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Auto-booking failed: {str(e)}")

@router.get("/booking-status")
async def get_booking_service_status():
    """Get booking service status and capabilities"""
    
    return {
        "status": "operational",
        "features": [
            "flight_booking",
            "hotel_booking", 
            "restaurant_reservations",
            "appointment_scheduling",
            "transportation_booking",
            "smart_suggestions",
            "auto_booking",
            "cancellation_management"
        ],
        "supported_providers": {
            "flights": ["Amadeus", "Expedia", "Kayak"],
            "hotels": ["Booking.com", "Hotels.com", "Airbnb"],
            "restaurants": ["OpenTable", "Resy", "Yelp"],
            "transportation": ["Uber", "Lyft", "Local Taxi"],
            "appointments": ["Calendly", "Acuity", "Square"]
        },
        "payment_methods": ["Credit Card", "PayPal", "Apple Pay", "Google Pay"],
        "cancellation_policies": {
            "flights": "24-hour free cancellation",
            "hotels": "Free cancellation until check-in",
            "restaurants": "2-hour advance notice",
            "appointments": "24-hour advance notice"
        }
    }
