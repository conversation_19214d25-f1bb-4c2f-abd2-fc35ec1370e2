import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, CircularProgress } from '@mui/material';
import { Provider } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';

// Store
import { store } from './store/store';

// Components
import Sidebar from './components/Sidebar';
import TopBar from './components/TopBar';
import VoiceInterface from './components/VoiceInterface';
import NotificationCenter from './components/NotificationCenter';
import LoadingScreen from './components/LoadingScreen';

// Pages
import Dashboard from './pages/Dashboard';
import VoiceAssistant from './pages/VoiceAssistant';
import ComputerVision from './pages/ComputerVision';
import Automation from './pages/Automation';
import SmartHome from './pages/SmartHome';
import Analytics from './pages/Analytics';
import Settings from './pages/Settings';
import DevTools from './pages/DevTools';

// Services
import { ElectronService } from './services/ElectronService';
import { ThemeService } from './services/ThemeService';

// Types
import { AppTheme } from './types/theme';

// Hooks
import { useAppSelector, useAppDispatch } from './hooks/redux';
import { setTheme, setLoading, setConnected } from './store/slices/appSlice';

const App: React.FC = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const dispatch = useAppDispatch();
  
  const { 
    theme: currentTheme, 
    isLoading, 
    isConnected,
    sidebarOpen 
  } = useAppSelector((state) => state.app);

  // Create MUI theme
  const muiTheme = createTheme({
    palette: {
      mode: currentTheme === 'dark' ? 'dark' : 'light',
      primary: {
        main: '#2196F3',
        dark: '#1976D2',
        light: '#42A5F5',
      },
      secondary: {
        main: '#4CAF50',
        dark: '#388E3C',
        light: '#66BB6A',
      },
      background: {
        default: currentTheme === 'dark' ? '#0a0a0a' : '#f5f5f5',
        paper: currentTheme === 'dark' ? '#1a1a1a' : '#ffffff',
      },
      text: {
        primary: currentTheme === 'dark' ? '#ffffff' : '#000000',
        secondary: currentTheme === 'dark' ? '#b0b0b0' : '#666666',
      },
    },
    typography: {
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontSize: '2.5rem',
        fontWeight: 600,
      },
      h2: {
        fontSize: '2rem',
        fontWeight: 600,
      },
      h3: {
        fontSize: '1.75rem',
        fontWeight: 500,
      },
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            scrollbarWidth: 'thin',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              background: currentTheme === 'dark' ? '#2a2a2a' : '#f1f1f1',
            },
            '&::-webkit-scrollbar-thumb': {
              background: currentTheme === 'dark' ? '#555' : '#888',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb:hover': {
              background: currentTheme === 'dark' ? '#777' : '#555',
            },
          },
        },
      },
    },
  });

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      dispatch(setLoading(true));

      // Initialize Electron services
      await ElectronService.initialize();

      // Load saved theme
      const savedTheme = await ElectronService.store.get('theme') as AppTheme;
      if (savedTheme) {
        dispatch(setTheme(savedTheme));
      }

      // Check backend connection
      const connectionStatus = await ElectronService.checkBackendConnection();
      dispatch(setConnected(connectionStatus));

      // Setup IPC listeners
      setupIpcListeners();

      setIsInitialized(true);
    } catch (error) {
      console.error('Failed to initialize app:', error);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const setupIpcListeners = () => {
    // Voice interface events
    ElectronService.onVoiceShowInterface(() => {
      // Show voice interface
    });

    // Vision analysis events
    ElectronService.onVisionScreenshotAnalysis((analysis) => {
      // Handle screenshot analysis
      console.log('Screenshot analysis:', analysis);
    });

    // Navigation events
    ElectronService.onNavigateTo((route) => {
      // Handle navigation
      window.location.hash = route;
    });

    // System events
    ElectronService.onSystemUpdate((update) => {
      // Handle system updates
      console.log('System update:', update);
    });
  };

  if (!isInitialized) {
    return <LoadingScreen />;
  }

  return (
    <Provider store={store}>
      <ThemeProvider theme={muiTheme}>
        <CssBaseline />
        <Router>
          <Box sx={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
            {/* Sidebar */}
            <AnimatePresence>
              {sidebarOpen && (
                <motion.div
                  initial={{ x: -280 }}
                  animate={{ x: 0 }}
                  exit={{ x: -280 }}
                  transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                  style={{ width: 280, flexShrink: 0 }}
                >
                  <Sidebar />
                </motion.div>
              )}
            </AnimatePresence>

            {/* Main Content */}
            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
              {/* Top Bar */}
              <TopBar />

              {/* Page Content */}
              <Box sx={{ flexGrow: 1, overflow: 'auto', position: 'relative' }}>
                <AnimatePresence mode="wait">
                  <Routes>
                    <Route path="/" element={<Navigate to="/dashboard" replace />} />
                    <Route 
                      path="/dashboard" 
                      element={
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Dashboard />
                        </motion.div>
                      } 
                    />
                    <Route 
                      path="/voice" 
                      element={
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <VoiceAssistant />
                        </motion.div>
                      } 
                    />
                    <Route 
                      path="/vision" 
                      element={
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <ComputerVision />
                        </motion.div>
                      } 
                    />
                    <Route 
                      path="/automation" 
                      element={
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Automation />
                        </motion.div>
                      } 
                    />
                    <Route 
                      path="/smart-home" 
                      element={
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <SmartHome />
                        </motion.div>
                      } 
                    />
                    <Route 
                      path="/analytics" 
                      element={
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Analytics />
                        </motion.div>
                      } 
                    />
                    <Route 
                      path="/settings" 
                      element={
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Settings />
                        </motion.div>
                      } 
                    />
                    <Route 
                      path="/dev-tools" 
                      element={
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <DevTools />
                        </motion.div>
                      } 
                    />
                  </Routes>
                </AnimatePresence>

                {/* Loading Overlay */}
                {isLoading && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(0, 0, 0, 0.5)',
                      zIndex: 9999,
                    }}
                  >
                    <CircularProgress size={60} />
                  </Box>
                )}
              </Box>
            </Box>

            {/* Voice Interface Overlay */}
            <VoiceInterface />

            {/* Notification Center */}
            <NotificationCenter />
          </Box>
        </Router>
      </ThemeProvider>
    </Provider>
  );
};

export default App;
