import React, { useEffect, useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Dimensions,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  FAB,
  Portal,
  Modal,
  Text,
  Surface,
  IconButton,
  Chip,
  ProgressBar,
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { Ionicons } from '@expo/vector-icons';

// Components
import VoiceWaveform from '../components/VoiceWaveform';
import QuickActions from '../components/QuickActions';
import StatusIndicator from '../components/StatusIndicator';
import WeatherWidget from '../components/WeatherWidget';
import TaskSummary from '../components/TaskSummary';

// Services
import { JarvisService } from '../services/JarvisService';
import { VoiceService } from '../services/VoiceService';
import { NotificationService } from '../services/NotificationService';

// Store
import { useAppStore } from '../store/appStore';
import { useVoiceStore } from '../store/voiceStore';

// Types
import { HomeScreenProps } from '../types/navigation';

const { width, height } = Dimensions.get('window');

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [showVoiceModal, setShowVoiceModal] = useState(false);
  const [systemStatus, setSystemStatus] = useState('online');
  
  const { 
    isConnected, 
    lastActivity, 
    notifications,
    systemHealth,
    updateSystemHealth 
  } = useAppStore();
  
  const { 
    isRecording, 
    transcript, 
    confidence,
    startListening,
    stopListening 
  } = useVoiceStore();

  useEffect(() => {
    // Initialize home screen
    initializeHomeScreen();
    
    // Set up periodic health checks
    const healthCheckInterval = setInterval(checkSystemHealth, 30000); // 30 seconds
    
    return () => {
      clearInterval(healthCheckInterval);
    };
  }, []);

  const initializeHomeScreen = async () => {
    try {
      await checkSystemHealth();
      await loadRecentActivity();
    } catch (error) {
      console.error('Failed to initialize home screen:', error);
    }
  };

  const checkSystemHealth = async () => {
    try {
      const health = await JarvisService.getSystemHealth();
      updateSystemHealth(health);
      
      if (health.status === 'critical') {
        Alert.alert(
          'System Alert',
          'JARVIS is experiencing issues. Some features may be unavailable.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Health check failed:', error);
      setSystemStatus('offline');
    }
  };

  const loadRecentActivity = async () => {
    try {
      // Load recent activity, notifications, etc.
      await NotificationService.loadRecentNotifications();
    } catch (error) {
      console.error('Failed to load recent activity:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await initializeHomeScreen();
    } finally {
      setRefreshing(false);
    }
  };

  const handleVoiceCommand = async () => {
    try {
      if (isRecording) {
        await stopListening();
        setShowVoiceModal(false);
      } else {
        setShowVoiceModal(true);
        await startListening();
      }
    } catch (error) {
      console.error('Voice command error:', error);
      Alert.alert('Error', 'Failed to process voice command');
    }
  };

  const quickActions = [
    {
      id: 'voice',
      title: 'Voice Command',
      icon: 'mic',
      color: '#4CAF50',
      onPress: handleVoiceCommand,
    },
    {
      id: 'camera',
      title: 'Vision Analysis',
      icon: 'camera',
      color: '#2196F3',
      onPress: () => navigation.navigate('Vision'),
    },
    {
      id: 'automation',
      title: 'Automation',
      icon: 'settings',
      color: '#FF9800',
      onPress: () => navigation.navigate('Automation'),
    },
    {
      id: 'emergency',
      title: 'Emergency',
      icon: 'alert-circle',
      color: '#F44336',
      onPress: () => {
        Alert.alert(
          'Emergency Mode',
          'Activate emergency protocols?',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Activate', style: 'destructive', onPress: () => {} },
          ]
        );
      },
    },
  ];

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.gradient}
      >
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
          {/* Header Section */}
          <Animatable.View animation="fadeInDown" style={styles.header}>
            <View style={styles.headerContent}>
              <View>
                <Title style={styles.welcomeText}>Welcome back</Title>
                <Paragraph style={styles.statusText}>
                  JARVIS is {systemStatus}
                </Paragraph>
              </View>
              <StatusIndicator status={systemStatus} />
            </View>
          </Animatable.View>

          {/* System Health */}
          <Animatable.View animation="fadeInUp" delay={200}>
            <Card style={styles.healthCard}>
              <Card.Content>
                <View style={styles.healthHeader}>
                  <Title>System Health</Title>
                  <Chip 
                    icon="heart" 
                    mode="outlined"
                    textStyle={{ color: systemHealth?.status === 'healthy' ? '#4CAF50' : '#F44336' }}
                  >
                    {systemHealth?.status || 'Unknown'}
                  </Chip>
                </View>
                
                {systemHealth && (
                  <View style={styles.healthMetrics}>
                    <View style={styles.metric}>
                      <Text>CPU Usage</Text>
                      <ProgressBar 
                        progress={systemHealth.cpu_usage / 100} 
                        color="#2196F3"
                        style={styles.progressBar}
                      />
                      <Text style={styles.metricValue}>{systemHealth.cpu_usage}%</Text>
                    </View>
                    
                    <View style={styles.metric}>
                      <Text>Memory Usage</Text>
                      <ProgressBar 
                        progress={systemHealth.memory_usage / 100} 
                        color="#4CAF50"
                        style={styles.progressBar}
                      />
                      <Text style={styles.metricValue}>{systemHealth.memory_usage}%</Text>
                    </View>
                  </View>
                )}
              </Card.Content>
            </Card>
          </Animatable.View>

          {/* Quick Actions */}
          <Animatable.View animation="fadeInUp" delay={400}>
            <QuickActions actions={quickActions} />
          </Animatable.View>

          {/* Weather Widget */}
          <Animatable.View animation="fadeInUp" delay={600}>
            <WeatherWidget />
          </Animatable.View>

          {/* Task Summary */}
          <Animatable.View animation="fadeInUp" delay={800}>
            <TaskSummary />
          </Animatable.View>

          {/* Recent Activity */}
          <Animatable.View animation="fadeInUp" delay={1000}>
            <Card style={styles.activityCard}>
              <Card.Content>
                <Title>Recent Activity</Title>
                {lastActivity && lastActivity.length > 0 ? (
                  lastActivity.slice(0, 3).map((activity, index) => (
                    <Surface key={index} style={styles.activityItem}>
                      <View style={styles.activityContent}>
                        <Ionicons 
                          name={activity.icon as any} 
                          size={24} 
                          color="#2196F3" 
                        />
                        <View style={styles.activityText}>
                          <Text style={styles.activityTitle}>{activity.title}</Text>
                          <Text style={styles.activityTime}>{activity.time}</Text>
                        </View>
                      </View>
                    </Surface>
                  ))
                ) : (
                  <Paragraph>No recent activity</Paragraph>
                )}
              </Card.Content>
            </Card>
          </Animatable.View>
        </ScrollView>

        {/* Voice Command FAB */}
        <FAB
          style={[
            styles.fab,
            { backgroundColor: isRecording ? '#F44336' : '#4CAF50' }
          ]}
          icon={isRecording ? 'stop' : 'mic'}
          onPress={handleVoiceCommand}
          animated
        />

        {/* Voice Modal */}
        <Portal>
          <Modal
            visible={showVoiceModal}
            onDismiss={() => setShowVoiceModal(false)}
            contentContainerStyle={styles.voiceModal}
          >
            <VoiceWaveform 
              isRecording={isRecording}
              transcript={transcript}
              confidence={confidence}
            />
            <Button
              mode="contained"
              onPress={() => setShowVoiceModal(false)}
              style={styles.closeButton}
            >
              Close
            </Button>
          </Modal>
        </Portal>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeText: {
    color: 'white',
    fontSize: 28,
    fontWeight: 'bold',
  },
  statusText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
  },
  healthCard: {
    marginBottom: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  healthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  healthMetrics: {
    gap: 12,
  },
  metric: {
    gap: 4,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  metricValue: {
    textAlign: 'right',
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  activityCard: {
    marginBottom: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  activityItem: {
    padding: 12,
    marginVertical: 4,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  activityContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  activityText: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
  activityTime: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 80,
  },
  voiceModal: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 16,
  },
  closeButton: {
    marginTop: 16,
  },
});

export default HomeScreen;
