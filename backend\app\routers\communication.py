"""Communication router for email, SMS, etc."""

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from ..core.database import User
from ..core.security import get_current_active_user

router = APIRouter()

class EmailRequest(BaseModel):
    to: str
    subject: str
    body: str

@router.post("/send-email")
async def send_email(
    email: EmailRequest,
    current_user: User = Depends(get_current_active_user)
):
    return {"message": "Email sent", "status": "success"}

@router.post("/send-sms")
async def send_sms(
    phone: str,
    message: str,
    current_user: User = Depends(get_current_active_user)
):
    return {"message": "SMS sent", "status": "success"}
