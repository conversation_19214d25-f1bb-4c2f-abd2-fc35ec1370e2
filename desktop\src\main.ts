import { app, BrowserWindow, ipcMain, Menu, Tray, globalShortcut, screen, dialog, shell } from 'electron';
import { autoUpdater } from 'electron-updater';
import * as path from 'path';
import * as isDev from 'electron-is-dev';
import Store from 'electron-store';

// Services
import { VoiceService } from './services/VoiceService';
import { VisionService } from './services/VisionService';
import { AutomationService } from './services/AutomationService';
import { SystemService } from './services/SystemService';

// Store
const store = new Store();

class JarvisDesktopApp {
  private mainWindow: BrowserWindow | null = null;
  private tray: Tray | null = null;
  private voiceService: VoiceService;
  private visionService: VisionService;
  private automationService: AutomationService;
  private systemService: SystemService;

  constructor() {
    this.voiceService = new VoiceService();
    this.visionService = new VisionService();
    this.automationService = new AutomationService();
    this.systemService = new SystemService();
  }

  async initialize() {
    // Set app user model ID for Windows
    if (process.platform === 'win32') {
      app.setAppUserModelId('com.jarvis.desktop');
    }

    // Handle app events
    app.whenReady().then(() => {
      this.createMainWindow();
      this.createTray();
      this.setupGlobalShortcuts();
      this.setupIpcHandlers();
      this.initializeServices();
      this.setupAutoUpdater();
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createMainWindow();
      }
    });

    app.on('before-quit', () => {
      this.cleanup();
    });
  }

  private createMainWindow() {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    // Restore window state
    const windowState = store.get('windowState', {
      width: Math.min(1400, width),
      height: Math.min(900, height),
      x: Math.floor((width - 1400) / 2),
      y: Math.floor((height - 900) / 2),
    });

    this.mainWindow = new BrowserWindow({
      ...windowState,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js'),
      },
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      frame: process.platform !== 'win32',
      show: false,
      icon: path.join(__dirname, '../assets/icon.png'),
    });

    // Load the app
    const startUrl = isDev 
      ? 'http://localhost:3000' 
      : `file://${path.join(__dirname, '../build/index.html')}`;
    
    this.mainWindow.loadURL(startUrl);

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
      
      if (isDev) {
        this.mainWindow?.webContents.openDevTools();
      }
    });

    // Save window state
    this.mainWindow.on('close', () => {
      if (this.mainWindow) {
        const bounds = this.mainWindow.getBounds();
        store.set('windowState', bounds);
      }
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });
  }

  private createTray() {
    const iconPath = path.join(__dirname, '../assets/tray-icon.png');
    this.tray = new Tray(iconPath);

    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Show JARVIS',
        click: () => {
          this.mainWindow?.show();
          this.mainWindow?.focus();
        },
      },
      {
        label: 'Voice Command',
        accelerator: 'CmdOrCtrl+Shift+J',
        click: () => {
          this.handleVoiceCommand();
        },
      },
      { type: 'separator' },
      {
        label: 'Settings',
        click: () => {
          this.mainWindow?.webContents.send('navigate-to', '/settings');
          this.mainWindow?.show();
        },
      },
      { type: 'separator' },
      {
        label: 'Quit JARVIS',
        click: () => {
          app.quit();
        },
      },
    ]);

    this.tray.setContextMenu(contextMenu);
    this.tray.setToolTip('JARVIS - AI Assistant');

    this.tray.on('click', () => {
      this.mainWindow?.isVisible() ? this.mainWindow.hide() : this.mainWindow?.show();
    });
  }

  private setupGlobalShortcuts() {
    // Global voice command shortcut
    globalShortcut.register('CmdOrCtrl+Shift+J', () => {
      this.handleVoiceCommand();
    });

    // Quick screenshot for vision analysis
    globalShortcut.register('CmdOrCtrl+Shift+V', () => {
      this.handleScreenshot();
    });

    // Show/hide main window
    globalShortcut.register('CmdOrCtrl+Shift+H', () => {
      if (this.mainWindow?.isVisible()) {
        this.mainWindow.hide();
      } else {
        this.mainWindow?.show();
        this.mainWindow?.focus();
      }
    });
  }

  private setupIpcHandlers() {
    // Voice service handlers
    ipcMain.handle('voice:start-listening', async () => {
      return await this.voiceService.startListening();
    });

    ipcMain.handle('voice:stop-listening', async () => {
      return await this.voiceService.stopListening();
    });

    ipcMain.handle('voice:synthesize', async (_, text: string) => {
      return await this.voiceService.synthesizeText(text);
    });

    // Vision service handlers
    ipcMain.handle('vision:analyze-image', async (_, imageData: string) => {
      return await this.visionService.analyzeImage(imageData);
    });

    ipcMain.handle('vision:start-camera', async () => {
      return await this.visionService.startCamera();
    });

    ipcMain.handle('vision:stop-camera', async () => {
      return await this.visionService.stopCamera();
    });

    // System service handlers
    ipcMain.handle('system:get-info', async () => {
      return await this.systemService.getSystemInfo();
    });

    ipcMain.handle('system:get-health', async () => {
      return await this.systemService.getSystemHealth();
    });

    // Automation service handlers
    ipcMain.handle('automation:execute-workflow', async (_, workflowId: string) => {
      return await this.automationService.executeWorkflow(workflowId);
    });

    ipcMain.handle('automation:get-workflows', async () => {
      return await this.automationService.getWorkflows();
    });

    // Window management
    ipcMain.handle('window:minimize', () => {
      this.mainWindow?.minimize();
    });

    ipcMain.handle('window:maximize', () => {
      if (this.mainWindow?.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow?.maximize();
      }
    });

    ipcMain.handle('window:close', () => {
      this.mainWindow?.close();
    });

    // File operations
    ipcMain.handle('dialog:open-file', async (_, options) => {
      const result = await dialog.showOpenDialog(this.mainWindow!, options);
      return result;
    });

    ipcMain.handle('dialog:save-file', async (_, options) => {
      const result = await dialog.showSaveDialog(this.mainWindow!, options);
      return result;
    });

    // Store operations
    ipcMain.handle('store:get', (_, key: string) => {
      return store.get(key);
    });

    ipcMain.handle('store:set', (_, key: string, value: any) => {
      store.set(key, value);
    });

    ipcMain.handle('store:delete', (_, key: string) => {
      store.delete(key);
    });
  }

  private async initializeServices() {
    try {
      await this.voiceService.initialize();
      await this.visionService.initialize();
      await this.automationService.initialize();
      await this.systemService.initialize();
      
      console.log('All services initialized successfully');
    } catch (error) {
      console.error('Failed to initialize services:', error);
    }
  }

  private setupAutoUpdater() {
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify();
      
      autoUpdater.on('update-available', () => {
        dialog.showMessageBox(this.mainWindow!, {
          type: 'info',
          title: 'Update Available',
          message: 'A new version of JARVIS is available. It will be downloaded in the background.',
          buttons: ['OK'],
        });
      });

      autoUpdater.on('update-downloaded', () => {
        dialog.showMessageBox(this.mainWindow!, {
          type: 'info',
          title: 'Update Ready',
          message: 'Update downloaded. JARVIS will restart to apply the update.',
          buttons: ['Restart Now', 'Later'],
        }).then((result) => {
          if (result.response === 0) {
            autoUpdater.quitAndInstall();
          }
        });
      });
    }
  }

  private async handleVoiceCommand() {
    try {
      const isListening = await this.voiceService.isListening();
      
      if (isListening) {
        await this.voiceService.stopListening();
      } else {
        await this.voiceService.startListening();
        
        // Show voice interface
        this.mainWindow?.webContents.send('voice:show-interface');
        this.mainWindow?.show();
        this.mainWindow?.focus();
      }
    } catch (error) {
      console.error('Voice command error:', error);
    }
  }

  private async handleScreenshot() {
    try {
      const screenshot = await this.visionService.takeScreenshot();
      const analysis = await this.visionService.analyzeImage(screenshot);
      
      // Send results to renderer
      this.mainWindow?.webContents.send('vision:screenshot-analysis', analysis);
      this.mainWindow?.show();
      this.mainWindow?.focus();
    } catch (error) {
      console.error('Screenshot analysis error:', error);
    }
  }

  private cleanup() {
    // Unregister global shortcuts
    globalShortcut.unregisterAll();
    
    // Cleanup services
    this.voiceService.cleanup();
    this.visionService.cleanup();
    this.automationService.cleanup();
    this.systemService.cleanup();
  }
}

// Initialize the app
const jarvisApp = new JarvisDesktopApp();
jarvisApp.initialize();
