#!/bin/bash

# JARVIS Deployment Script
# Ultra-Advanced AI Assistant with Zero Human Intervention

set -e

echo "🚀 Starting JARVIS Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Check if .env file exists
check_env() {
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from template..."
        cp .env.example .env
        print_warning "Please edit .env file with your API keys before continuing."
        print_warning "Required keys: OPENAI_API_KEY, SUPABASE_URL, SUPABASE_KEY"
        read -p "Press Enter after updating .env file..."
    fi
    print_success ".env file found"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p livekit
    mkdir -p nginx
    mkdir -p monitoring
    mkdir -p database
    
    print_success "Directories created"
}

# Create LiveKit configuration
create_livekit_config() {
    print_status "Creating LiveKit configuration..."
    
    cat > livekit/livekit.yaml << EOF
port: 7880
bind_addresses:
  - ""
rtc:
  tcp_port: 7881
  port_range_start: 50000
  port_range_end: 60000
  use_external_ip: true
redis:
  address: redis:6379
turn:
  enabled: true
  domain: localhost
  cert_file: ""
  key_file: ""
  tls_port: 5349
  udp_port: 3478
keys:
  APIKey: devkey
  APISecret: secret
room:
  max_participants: 100
  empty_timeout: 300s
EOF
    
    print_success "LiveKit configuration created"
}

# Create Nginx configuration
create_nginx_config() {
    print_status "Creating Nginx configuration..."
    
    mkdir -p nginx/ssl
    
    cat > nginx/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }
    
    upstream frontend {
        server frontend:3000;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        # Backend API
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        # WebSocket support
        location /ws {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade \$http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
    }
}
EOF
    
    print_success "Nginx configuration created"
}

# Create Prometheus configuration
create_prometheus_config() {
    print_status "Creating Prometheus configuration..."
    
    cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'jarvis-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
      
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
      
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
EOF
    
    print_success "Prometheus configuration created"
}

# Create database initialization script
create_db_init() {
    print_status "Creating database initialization script..."
    
    cat > database/init.sql << EOF
-- JARVIS Database Initialization

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_conversations_session_id ON conversations(session_id);
CREATE INDEX IF NOT EXISTS idx_commands_user_id ON commands(user_id);
CREATE INDEX IF NOT EXISTS idx_devices_user_id ON devices(user_id);
CREATE INDEX IF NOT EXISTS idx_workflows_user_id ON workflows(user_id);

-- Create full-text search indexes
CREATE INDEX IF NOT EXISTS idx_conversations_message_fts ON conversations USING gin(to_tsvector('english', message));
CREATE INDEX IF NOT EXISTS idx_conversations_response_fts ON conversations USING gin(to_tsvector('english', response));

-- Create health records table
CREATE TABLE IF NOT EXISTS health_records (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    record_type VARCHAR(50) NOT NULL,
    data JSONB NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    booking_id VARCHAR(100) UNIQUE NOT NULL,
    booking_type VARCHAR(50) NOT NULL,
    booking_data JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create emergency contacts table
CREATE TABLE IF NOT EXISTS emergency_contacts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    relationship VARCHAR(50),
    priority INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER UNIQUE NOT NULL,
    preferences JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create analytics table
CREATE TABLE IF NOT EXISTS analytics (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, hashed_password, full_name, is_active, is_superuser) 
VALUES ('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJflLxQjO', 'JARVIS Administrator', true, true)
ON CONFLICT (username) DO NOTHING;

COMMIT;
EOF
    
    print_success "Database initialization script created"
}

# Build and start services
start_services() {
    print_status "Building and starting JARVIS services..."
    
    # Pull latest images
    docker-compose pull
    
    # Build custom images
    docker-compose build --no-cache
    
    # Start services
    docker-compose up -d
    
    print_success "All services started"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for database
    print_status "Waiting for PostgreSQL..."
    until docker-compose exec -T postgres pg_isready -U jarvis; do
        sleep 2
    done
    
    # Wait for Redis
    print_status "Waiting for Redis..."
    until docker-compose exec -T redis redis-cli ping; do
        sleep 2
    done
    
    # Wait for backend
    print_status "Waiting for backend..."
    until curl -f http://localhost:8000/health; do
        sleep 5
    done
    
    print_success "All services are ready"
}

# Display service URLs
show_urls() {
    print_success "🎉 JARVIS is now running!"
    echo ""
    echo "📱 Access your services:"
    echo "   • JARVIS Frontend:     http://localhost:3000"
    echo "   • API Documentation:   http://localhost:8000/docs"
    echo "   • N8N Workflows:       http://localhost:5678 (admin/admin123)"
    echo "   • Grafana Monitoring:  http://localhost:3001 (admin/admin123)"
    echo "   • Prometheus Metrics:  http://localhost:9090"
    echo ""
    echo "🔧 Admin Access:"
    echo "   • Username: admin"
    echo "   • Password: admin123"
    echo ""
    echo "📚 Documentation: Check README.md for detailed usage instructions"
    echo ""
    print_warning "⚠️  Remember to:"
    echo "   1. Update your .env file with real API keys"
    echo "   2. Change default passwords in production"
    echo "   3. Configure SSL certificates for HTTPS"
    echo "   4. Set up proper backup procedures"
}

# Main deployment function
main() {
    echo "🤖 JARVIS - Ultra-Advanced AI Assistant"
    echo "🚀 Zero Human Intervention Deployment"
    echo "=================================="
    echo ""
    
    check_docker
    check_env
    create_directories
    create_livekit_config
    create_nginx_config
    create_prometheus_config
    create_db_init
    start_services
    wait_for_services
    show_urls
    
    print_success "🎯 Deployment completed successfully!"
}

# Run main function
main "$@"
