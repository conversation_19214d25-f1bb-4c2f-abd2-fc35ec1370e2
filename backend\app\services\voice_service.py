"""
Advanced Voice Processing Service for JARVIS
Handles speech recognition, text-to-speech, and voice analysis
"""

import asyncio
import io
import wave
import tempfile
import os
from typing import Dict, List, Any, Optional, Union
import speech_recognition as sr
import whisper
from gtts import gTTS
import pygame
from pydub import AudioSegment
import numpy as np
from loguru import logger

from ..core.config import settings

class VoiceService:
    """Advanced voice processing service"""
    
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.whisper_model = None
        self.supported_languages = [
            'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'
        ]
        self.voice_profiles = {}
        
    async def initialize(self):
        """Initialize voice service"""
        try:
            # Initialize pygame mixer for audio playback
            pygame.mixer.init()
            
            # Load Whisper model for advanced speech recognition
            self.whisper_model = whisper.load_model("base")
            
            # Initialize microphone
            try:
                self.microphone = sr.Microphone()
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)
            except Exception as e:
                logger.warning(f"Microphone initialization failed: {e}")
            
            logger.info("Voice service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize voice service: {e}")
            raise
    
    async def speech_to_text(
        self,
        audio_data: Union[bytes, str],
        language: str = "en",
        use_whisper: bool = True
    ) -> Dict[str, Any]:
        """Convert speech to text using multiple recognition engines"""
        try:
            if isinstance(audio_data, str):
                # Audio file path
                audio_file = audio_data
            else:
                # Audio bytes - save to temporary file
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    temp_file.write(audio_data)
                    audio_file = temp_file.name
            
            results = {}
            
            # Use Whisper for high-quality recognition
            if use_whisper and self.whisper_model:
                try:
                    whisper_result = self.whisper_model.transcribe(
                        audio_file,
                        language=language if language != "auto" else None
                    )
                    results["whisper"] = {
                        "text": whisper_result["text"].strip(),
                        "confidence": 0.95,  # Whisper doesn't provide confidence
                        "language": whisper_result.get("language", language)
                    }
                except Exception as e:
                    logger.error(f"Whisper recognition failed: {e}")
            
            # Use Google Speech Recognition as fallback
            try:
                with sr.AudioFile(audio_file) as source:
                    audio = self.recognizer.record(source)
                
                google_text = self.recognizer.recognize_google(
                    audio,
                    language=language if language != "auto" else None
                )
                
                results["google"] = {
                    "text": google_text,
                    "confidence": 0.85,
                    "language": language
                }
            except Exception as e:
                logger.error(f"Google recognition failed: {e}")
            
            # Clean up temporary file
            if isinstance(audio_data, bytes) and os.path.exists(audio_file):
                os.unlink(audio_file)
            
            # Return best result
            if "whisper" in results:
                return results["whisper"]
            elif "google" in results:
                return results["google"]
            else:
                return {"text": "", "confidence": 0.0, "language": language}
                
        except Exception as e:
            logger.error(f"Speech recognition error: {e}")
            return {"text": "", "confidence": 0.0, "language": language, "error": str(e)}
    
    async def text_to_speech(
        self,
        text: str,
        language: str = "en",
        voice_speed: float = 1.0,
        voice_pitch: float = 1.0
    ) -> bytes:
        """Convert text to speech"""
        try:
            # Use gTTS for text-to-speech
            tts = gTTS(text=text, lang=language, slow=False)
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                tts.save(temp_file.name)
                temp_file_path = temp_file.name
            
            # Load and process audio
            audio = AudioSegment.from_mp3(temp_file_path)
            
            # Adjust speed
            if voice_speed != 1.0:
                audio = audio.speedup(playback_speed=voice_speed)
            
            # Convert to WAV bytes
            wav_io = io.BytesIO()
            audio.export(wav_io, format="wav")
            wav_bytes = wav_io.getvalue()
            
            # Clean up
            os.unlink(temp_file_path)
            
            return wav_bytes
            
        except Exception as e:
            logger.error(f"Text-to-speech error: {e}")
            return b""
    
    async def play_audio(self, audio_data: bytes) -> bool:
        """Play audio data"""
        try:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            # Play using pygame
            pygame.mixer.music.load(temp_file_path)
            pygame.mixer.music.play()
            
            # Wait for playback to finish
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.1)
            
            # Clean up
            os.unlink(temp_file_path)
            
            return True
            
        except Exception as e:
            logger.error(f"Audio playback error: {e}")
            return False
    
    async def record_audio(self, duration: float = 5.0) -> Optional[bytes]:
        """Record audio from microphone"""
        try:
            if not self.microphone:
                logger.error("Microphone not available")
                return None
            
            with self.microphone as source:
                logger.info(f"Recording audio for {duration} seconds...")
                audio = self.recognizer.listen(source, timeout=duration, phrase_time_limit=duration)
            
            # Convert to WAV bytes
            wav_data = audio.get_wav_data()
            return wav_data
            
        except Exception as e:
            logger.error(f"Audio recording error: {e}")
            return None
    
    async def analyze_voice(self, audio_data: bytes) -> Dict[str, Any]:
        """Analyze voice characteristics"""
        try:
            # Save to temporary file for analysis
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            # Load audio for analysis
            audio = AudioSegment.from_wav(temp_file_path)
            
            # Basic audio analysis
            analysis = {
                "duration": len(audio) / 1000.0,  # seconds
                "sample_rate": audio.frame_rate,
                "channels": audio.channels,
                "loudness": audio.dBFS,
                "max_loudness": audio.max_dBFS,
                "rms": audio.rms
            }
            
            # Convert to numpy array for advanced analysis
            samples = np.array(audio.get_array_of_samples())
            if audio.channels == 2:
                samples = samples.reshape((-1, 2))
                samples = samples.mean(axis=1)  # Convert to mono
            
            # Calculate additional features
            analysis.update({
                "zero_crossing_rate": self._calculate_zcr(samples),
                "spectral_centroid": self._calculate_spectral_centroid(samples, audio.frame_rate),
                "energy": np.sum(samples ** 2) / len(samples)
            })
            
            # Clean up
            os.unlink(temp_file_path)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Voice analysis error: {e}")
            return {}
    
    def _calculate_zcr(self, samples: np.ndarray) -> float:
        """Calculate zero crossing rate"""
        try:
            signs = np.sign(samples)
            zero_crossings = np.sum(np.abs(np.diff(signs))) / 2
            return zero_crossings / len(samples)
        except:
            return 0.0
    
    def _calculate_spectral_centroid(self, samples: np.ndarray, sample_rate: int) -> float:
        """Calculate spectral centroid"""
        try:
            # Simple spectral centroid calculation
            fft = np.abs(np.fft.fft(samples))
            freqs = np.fft.fftfreq(len(fft), 1/sample_rate)
            
            # Only use positive frequencies
            positive_freqs = freqs[:len(freqs)//2]
            positive_fft = fft[:len(fft)//2]
            
            if np.sum(positive_fft) == 0:
                return 0.0
            
            centroid = np.sum(positive_freqs * positive_fft) / np.sum(positive_fft)
            return centroid
        except:
            return 0.0
    
    async def create_voice_profile(self, user_id: str, audio_samples: List[bytes]) -> Dict[str, Any]:
        """Create a voice profile for a user"""
        try:
            if len(audio_samples) < 3:
                raise ValueError("At least 3 audio samples required for voice profile")
            
            analyses = []
            for audio_data in audio_samples:
                analysis = await self.analyze_voice(audio_data)
                if analysis:
                    analyses.append(analysis)
            
            if not analyses:
                raise ValueError("No valid audio samples for analysis")
            
            # Calculate average characteristics
            profile = {
                "user_id": user_id,
                "sample_count": len(analyses),
                "avg_duration": np.mean([a["duration"] for a in analyses]),
                "avg_loudness": np.mean([a["loudness"] for a in analyses]),
                "avg_zcr": np.mean([a["zero_crossing_rate"] for a in analyses]),
                "avg_spectral_centroid": np.mean([a["spectral_centroid"] for a in analyses]),
                "created_at": asyncio.get_event_loop().time()
            }
            
            self.voice_profiles[user_id] = profile
            logger.info(f"Created voice profile for user {user_id}")
            
            return profile
            
        except Exception as e:
            logger.error(f"Voice profile creation error: {e}")
            return {}
    
    async def verify_voice(self, user_id: str, audio_data: bytes) -> Dict[str, Any]:
        """Verify voice against stored profile"""
        try:
            if user_id not in self.voice_profiles:
                return {"verified": False, "reason": "No voice profile found"}
            
            profile = self.voice_profiles[user_id]
            analysis = await self.analyze_voice(audio_data)
            
            if not analysis:
                return {"verified": False, "reason": "Audio analysis failed"}
            
            # Simple similarity calculation
            features = ["avg_loudness", "avg_zcr", "avg_spectral_centroid"]
            similarities = []
            
            for feature in features:
                if feature in profile and feature.replace("avg_", "") in analysis:
                    profile_val = profile[feature]
                    analysis_val = analysis[feature.replace("avg_", "")]
                    
                    if profile_val != 0:
                        similarity = 1 - abs(profile_val - analysis_val) / abs(profile_val)
                        similarities.append(max(0, similarity))
            
            if not similarities:
                return {"verified": False, "reason": "No comparable features"}
            
            overall_similarity = np.mean(similarities)
            threshold = 0.7  # Adjust based on requirements
            
            return {
                "verified": overall_similarity >= threshold,
                "confidence": overall_similarity,
                "threshold": threshold,
                "features_compared": len(similarities)
            }
            
        except Exception as e:
            logger.error(f"Voice verification error: {e}")
            return {"verified": False, "reason": f"Error: {str(e)}"}
    
    async def cleanup(self):
        """Cleanup voice service"""
        try:
            if pygame.mixer.get_init():
                pygame.mixer.quit()
            logger.info("Voice service cleanup completed")
        except Exception as e:
            logger.error(f"Voice service cleanup error: {e}")
