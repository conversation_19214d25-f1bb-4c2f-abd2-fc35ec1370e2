"""
Redis client configuration and utilities
"""

import json
import asyncio
from typing import Any, Optional, Dict, List
import redis.asyncio as redis
from loguru import logger

from .config import settings

class RedisClient:
    """Redis client wrapper"""
    
    def __init__(self):
        self.redis: Optional[redis.Redis] = None
        self.pubsub: Optional[redis.client.PubSub] = None
    
    async def connect(self):
        """Connect to Redis"""
        try:
            self.redis = redis.from_url(settings.REDIS_URL, decode_responses=True)
            await self.redis.ping()
            logger.info("Connected to Redis successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis:
            await self.redis.close()
            logger.info("Disconnected from Redis")
    
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """Set a key-value pair"""
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            
            result = await self.redis.set(key, value, ex=expire)
            return result
        except Exception as e:
            logger.error(f"Error setting Redis key {key}: {e}")
            return False
    
    async def get(self, key: str, default: Any = None) -> Any:
        """Get a value by key"""
        try:
            value = await self.redis.get(key)
            if value is None:
                return default
            
            # Try to parse as JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        except Exception as e:
            logger.error(f"Error getting Redis key {key}: {e}")
            return default
    
    async def delete(self, key: str) -> bool:
        """Delete a key"""
        try:
            result = await self.redis.delete(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Error deleting Redis key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        try:
            result = await self.redis.exists(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Error checking Redis key {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration for a key"""
        try:
            result = await self.redis.expire(key, seconds)
            return bool(result)
        except Exception as e:
            logger.error(f"Error setting expiration for Redis key {key}: {e}")
            return False
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """Increment a key"""
        try:
            result = await self.redis.incr(key, amount)
            return result
        except Exception as e:
            logger.error(f"Error incrementing Redis key {key}: {e}")
            return 0
    
    async def lpush(self, key: str, *values) -> int:
        """Push values to the left of a list"""
        try:
            serialized_values = []
            for value in values:
                if isinstance(value, (dict, list)):
                    serialized_values.append(json.dumps(value))
                else:
                    serialized_values.append(str(value))
            
            result = await self.redis.lpush(key, *serialized_values)
            return result
        except Exception as e:
            logger.error(f"Error pushing to Redis list {key}: {e}")
            return 0
    
    async def rpop(self, key: str) -> Any:
        """Pop value from the right of a list"""
        try:
            value = await self.redis.rpop(key)
            if value is None:
                return None
            
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        except Exception as e:
            logger.error(f"Error popping from Redis list {key}: {e}")
            return None
    
    async def lrange(self, key: str, start: int = 0, end: int = -1) -> List[Any]:
        """Get range of values from a list"""
        try:
            values = await self.redis.lrange(key, start, end)
            result = []
            for value in values:
                try:
                    result.append(json.loads(value))
                except (json.JSONDecodeError, TypeError):
                    result.append(value)
            return result
        except Exception as e:
            logger.error(f"Error getting range from Redis list {key}: {e}")
            return []
    
    async def publish(self, channel: str, message: Any) -> int:
        """Publish message to a channel"""
        try:
            if isinstance(message, (dict, list)):
                message = json.dumps(message)
            
            result = await self.redis.publish(channel, message)
            return result
        except Exception as e:
            logger.error(f"Error publishing to Redis channel {channel}: {e}")
            return 0
    
    async def subscribe(self, *channels) -> redis.client.PubSub:
        """Subscribe to channels"""
        try:
            self.pubsub = self.redis.pubsub()
            await self.pubsub.subscribe(*channels)
            return self.pubsub
        except Exception as e:
            logger.error(f"Error subscribing to Redis channels: {e}")
            raise
    
    async def cache_user_session(self, user_id: int, session_data: Dict[str, Any], expire: int = 3600):
        """Cache user session data"""
        key = f"user_session:{user_id}"
        await self.set(key, session_data, expire)
    
    async def get_user_session(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user session data"""
        key = f"user_session:{user_id}"
        return await self.get(key)
    
    async def invalidate_user_session(self, user_id: int):
        """Invalidate user session"""
        key = f"user_session:{user_id}"
        await self.delete(key)
    
    async def cache_conversation(self, user_id: int, conversation_id: str, messages: List[Dict[str, Any]]):
        """Cache conversation messages"""
        key = f"conversation:{user_id}:{conversation_id}"
        await self.set(key, messages, expire=86400)  # 24 hours
    
    async def get_conversation(self, user_id: int, conversation_id: str) -> List[Dict[str, Any]]:
        """Get cached conversation"""
        key = f"conversation:{user_id}:{conversation_id}"
        return await self.get(key, [])

# Global Redis client instance
redis_client = RedisClient()

async def init_redis():
    """Initialize Redis connection"""
    await redis_client.connect()

async def get_redis() -> RedisClient:
    """Get Redis client dependency"""
    return redis_client
