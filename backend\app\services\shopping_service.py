"""
Ultra-Advanced Autonomous Shopping & E-commerce Service for JARVIS
Features: Intelligent shopping, price comparison, automatic ordering, inventory management, financial optimization
"""

import asyncio
import json
import time
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
from loguru import logger

# E-commerce and shopping imports
try:
    import pandas as pd
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    logger.warning("Machine learning libraries not available")

try:
    import stripe
    import paypal
    PAYMENT_AVAILABLE = True
except ImportError:
    PAYMENT_AVAILABLE = False
    logger.warning("Payment processing libraries not available")

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from bs4 import BeautifulSoup
    WEB_SCRAPING_AVAILABLE = True
except ImportError:
    WEB_SCRAPING_AVAILABLE = False
    logger.warning("Web scraping libraries not available")

from ..core.config import settings
from ..core.redis_client import redis_client

class ProductCategory(Enum):
    """Product categories"""
    ELECTRONICS = "electronics"
    CLOTHING = "clothing"
    FOOD_BEVERAGES = "food_beverages"
    HOME_GARDEN = "home_garden"
    HEALTH_BEAUTY = "health_beauty"
    BOOKS_MEDIA = "books_media"
    SPORTS_OUTDOORS = "sports_outdoors"
    AUTOMOTIVE = "automotive"
    TOYS_GAMES = "toys_games"
    OFFICE_SUPPLIES = "office_supplies"

class ShoppingPlatform(Enum):
    """Supported shopping platforms"""
    AMAZON = "amazon"
    EBAY = "ebay"
    WALMART = "walmart"
    TARGET = "target"
    BESTBUY = "bestbuy"
    COSTCO = "costco"
    ALIBABA = "alibaba"
    ETSY = "etsy"
    SHOPIFY = "shopify"
    LOCAL_STORES = "local_stores"

class OrderStatus(Enum):
    """Order status types"""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    PROCESSING = "processing"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"
    RETURNED = "returned"

@dataclass
class Product:
    """Product information"""
    id: str
    name: str
    description: str
    category: ProductCategory
    brand: str
    price: float
    currency: str
    availability: bool
    rating: float
    review_count: int
    images: List[str]
    specifications: Dict[str, Any]
    platform: ShoppingPlatform
    url: str
    last_updated: datetime

@dataclass
class ShoppingPreferences:
    """User shopping preferences"""
    user_id: str
    preferred_brands: List[str]
    budget_limits: Dict[ProductCategory, float]
    quality_preference: str  # budget, balanced, premium
    delivery_preference: str  # standard, express, same_day
    payment_methods: List[str]
    preferred_platforms: List[ShoppingPlatform]
    dietary_restrictions: List[str]
    size_preferences: Dict[str, str]
    color_preferences: List[str]
    auto_reorder_enabled: bool
    price_alert_threshold: float

@dataclass
class ShoppingList:
    """Shopping list item"""
    id: str
    user_id: str
    product_name: str
    category: ProductCategory
    quantity: int
    max_price: Optional[float]
    priority: int  # 1-5
    auto_purchase: bool
    notes: str
    created_at: datetime
    target_date: Optional[datetime]

@dataclass
class Order:
    """Order information"""
    id: str
    user_id: str
    platform: ShoppingPlatform
    products: List[Dict[str, Any]]
    total_amount: float
    currency: str
    status: OrderStatus
    tracking_number: Optional[str]
    estimated_delivery: Optional[datetime]
    actual_delivery: Optional[datetime]
    payment_method: str
    shipping_address: Dict[str, str]
    created_at: datetime
    updated_at: datetime

@dataclass
class PriceAlert:
    """Price monitoring alert"""
    id: str
    user_id: str
    product_id: str
    target_price: float
    current_price: float
    price_drop_percentage: float
    alert_triggered: bool
    created_at: datetime

class UltraAdvancedShoppingService:
    """Ultra-advanced autonomous shopping and e-commerce service"""
    
    def __init__(self):
        # Shopping data management
        self.shopping_preferences: Dict[str, ShoppingPreferences] = {}
        self.shopping_lists: Dict[str, List[ShoppingList]] = {}
        self.orders: List[Order] = []
        self.price_alerts: List[PriceAlert] = []
        
        # Product database
        self.products: Dict[str, Product] = {}
        self.product_cache: Dict[str, List[Product]] = {}
        
        # AI and ML models
        self.price_predictor = None
        self.recommendation_engine = None
        self.demand_forecaster = None
        self.budget_optimizer = None
        
        # Platform integrations
        self.platform_clients: Dict[ShoppingPlatform, Any] = {}
        self.payment_processors: Dict[str, Any] = {}
        
        # Web scraping and monitoring
        self.price_monitors: Dict[str, Any] = {}
        self.inventory_trackers: Dict[str, Any] = {}
        
        # Autonomous shopping
        self.auto_purchase_engine = None
        self.inventory_manager = None
        
        # Financial optimization
        self.deal_finder = None
        self.coupon_manager = None
        self.cashback_tracker = None
        
        # Real-time monitoring
        self.monitoring_active = False
        self.monitoring_tasks: List[asyncio.Task] = []

    async def initialize(self):
        """Initialize ultra-advanced shopping service"""
        try:
            logger.info("🛒 Initializing Ultra-Advanced Shopping Service...")
            
            # Initialize ML models
            await self._initialize_ml_models()
            
            # Initialize platform integrations
            await self._initialize_platform_integrations()
            
            # Initialize payment processors
            await self._initialize_payment_processors()
            
            # Initialize web scraping
            await self._initialize_web_scraping()
            
            # Initialize autonomous shopping
            await self._initialize_autonomous_shopping()
            
            # Initialize financial optimization
            await self._initialize_financial_optimization()
            
            # Load existing data
            await self._load_shopping_data()
            
            # Start monitoring systems
            await self.start_monitoring()
            
            logger.info("🎯 Ultra-Advanced Shopping Service initialized successfully!")
            
        except Exception as e:
            logger.error(f"Failed to initialize shopping service: {e}")
            raise

    async def _initialize_ml_models(self):
        """Initialize machine learning models"""
        try:
            if not ML_AVAILABLE:
                logger.warning("ML libraries not available")
                return
            
            # Price prediction model
            self.price_predictor = PricePredictionModel()
            await self.price_predictor.initialize()
            
            # Product recommendation engine
            self.recommendation_engine = ProductRecommendationEngine()
            await self.recommendation_engine.initialize()
            
            # Demand forecasting
            self.demand_forecaster = DemandForecastingModel()
            await self.demand_forecaster.initialize()
            
            # Budget optimization
            self.budget_optimizer = BudgetOptimizer()
            await self.budget_optimizer.initialize()
            
            logger.info("✅ ML models initialized")
            
        except Exception as e:
            logger.error(f"ML models initialization error: {e}")

    async def _initialize_platform_integrations(self):
        """Initialize shopping platform integrations"""
        try:
            # Initialize platform clients
            self.platform_clients = {
                ShoppingPlatform.AMAZON: AmazonClient(),
                ShoppingPlatform.EBAY: EbayClient(),
                ShoppingPlatform.WALMART: WalmartClient(),
                ShoppingPlatform.TARGET: TargetClient(),
                ShoppingPlatform.BESTBUY: BestBuyClient(),
            }
            
            for client in self.platform_clients.values():
                await client.initialize()
            
            logger.info("✅ Platform integrations initialized")
            
        except Exception as e:
            logger.error(f"Platform integrations initialization error: {e}")

    async def _initialize_payment_processors(self):
        """Initialize payment processors"""
        try:
            if PAYMENT_AVAILABLE:
                # Initialize Stripe
                if hasattr(settings, 'STRIPE_SECRET_KEY'):
                    stripe.api_key = settings.STRIPE_SECRET_KEY
                    self.payment_processors['stripe'] = stripe
                
                # Initialize PayPal
                if hasattr(settings, 'PAYPAL_CLIENT_ID'):
                    self.payment_processors['paypal'] = PayPalClient()
                    await self.payment_processors['paypal'].initialize()
            
            logger.info("✅ Payment processors initialized")
            
        except Exception as e:
            logger.error(f"Payment processors initialization error: {e}")

    async def _initialize_web_scraping(self):
        """Initialize web scraping for price monitoring"""
        try:
            if WEB_SCRAPING_AVAILABLE:
                # Initialize price monitors for different platforms
                self.price_monitors = {
                    'amazon': AmazonPriceMonitor(),
                    'ebay': EbayPriceMonitor(),
                    'walmart': WalmartPriceMonitor(),
                }
                
                for monitor in self.price_monitors.values():
                    await monitor.initialize()
            
            logger.info("✅ Web scraping initialized")
            
        except Exception as e:
            logger.error(f"Web scraping initialization error: {e}")

    async def _initialize_autonomous_shopping(self):
        """Initialize autonomous shopping systems"""
        try:
            self.auto_purchase_engine = AutoPurchaseEngine()
            await self.auto_purchase_engine.initialize()
            
            self.inventory_manager = InventoryManager()
            await self.inventory_manager.initialize()
            
            logger.info("✅ Autonomous shopping initialized")
            
        except Exception as e:
            logger.error(f"Autonomous shopping initialization error: {e}")

    async def _initialize_financial_optimization(self):
        """Initialize financial optimization systems"""
        try:
            self.deal_finder = DealFinder()
            await self.deal_finder.initialize()
            
            self.coupon_manager = CouponManager()
            await self.coupon_manager.initialize()
            
            self.cashback_tracker = CashbackTracker()
            await self.cashback_tracker.initialize()
            
            logger.info("✅ Financial optimization initialized")
            
        except Exception as e:
            logger.error(f"Financial optimization initialization error: {e}")

    async def search_products(self, query: str, category: Optional[ProductCategory] = None,
                            max_price: Optional[float] = None, platforms: List[ShoppingPlatform] = None) -> List[Product]:
        """Search for products across multiple platforms"""
        try:
            if not platforms:
                platforms = list(self.platform_clients.keys())
            
            all_products = []
            
            # Search across all specified platforms
            for platform in platforms:
                if platform in self.platform_clients:
                    client = self.platform_clients[platform]
                    products = await client.search_products(query, category, max_price)
                    all_products.extend(products)
            
            # Remove duplicates and sort by relevance
            unique_products = self._deduplicate_products(all_products)
            sorted_products = self._sort_products_by_relevance(unique_products, query)
            
            # Cache results
            cache_key = f"search_{hash(query)}_{category}_{max_price}"
            self.product_cache[cache_key] = sorted_products
            
            logger.info(f"Found {len(sorted_products)} products for query: {query}")
            return sorted_products
            
        except Exception as e:
            logger.error(f"Product search error: {e}")
            return []

    async def compare_prices(self, product_name: str, platforms: List[ShoppingPlatform] = None) -> Dict[str, Any]:
        """Compare prices across multiple platforms"""
        try:
            if not platforms:
                platforms = list(self.platform_clients.keys())

            price_comparison = {
                "product_name": product_name,
                "platforms": {},
                "best_deal": None,
                "price_range": {"min": float('inf'), "max": 0},
                "average_price": 0,
                "savings_opportunity": 0
            }

            all_prices = []

            for platform in platforms:
                if platform in self.platform_clients:
                    client = self.platform_clients[platform]
                    products = await client.search_products(product_name, limit=5)

                    if products:
                        best_product = min(products, key=lambda p: p.price)
                        price_comparison["platforms"][platform.value] = {
                            "price": best_product.price,
                            "product": asdict(best_product),
                            "availability": best_product.availability
                        }
                        all_prices.append(best_product.price)

            if all_prices:
                min_price = min(all_prices)
                max_price = max(all_prices)
                avg_price = sum(all_prices) / len(all_prices)

                price_comparison["price_range"] = {"min": min_price, "max": max_price}
                price_comparison["average_price"] = avg_price
                price_comparison["savings_opportunity"] = max_price - min_price

                # Find best deal
                best_platform = min(price_comparison["platforms"].items(),
                                  key=lambda x: x[1]["price"])
                price_comparison["best_deal"] = {
                    "platform": best_platform[0],
                    "price": best_platform[1]["price"],
                    "savings": max_price - best_platform[1]["price"]
                }

            return price_comparison

        except Exception as e:
            logger.error(f"Price comparison error: {e}")
            return {}

    async def create_shopping_list(self, user_id: str, items: List[Dict[str, Any]]) -> str:
        """Create shopping list for user"""
        try:
            if user_id not in self.shopping_lists:
                self.shopping_lists[user_id] = []

            for item_data in items:
                item = ShoppingList(
                    id=f"item_{int(time.time())}_{hash(item_data['product_name'])}",
                    user_id=user_id,
                    product_name=item_data["product_name"],
                    category=ProductCategory(item_data.get("category", "electronics")),
                    quantity=item_data.get("quantity", 1),
                    max_price=item_data.get("max_price"),
                    priority=item_data.get("priority", 3),
                    auto_purchase=item_data.get("auto_purchase", False),
                    notes=item_data.get("notes", ""),
                    created_at=datetime.now(),
                    target_date=datetime.fromisoformat(item_data["target_date"]) if item_data.get("target_date") else None
                )

                self.shopping_lists[user_id].append(item)

            # Store in database
            await redis_client.set(f"shopping_list:{user_id}",
                                 [asdict(item) for item in self.shopping_lists[user_id]])

            logger.info(f"Created shopping list with {len(items)} items for user {user_id}")
            return f"shopping_list_{user_id}"

        except Exception as e:
            logger.error(f"Shopping list creation error: {e}")
            return ""

    async def auto_purchase_item(self, user_id: str, item_id: str) -> Optional[str]:
        """Automatically purchase item from shopping list"""
        try:
            if user_id not in self.shopping_lists:
                return None

            # Find the item
            item = None
            for shopping_item in self.shopping_lists[user_id]:
                if shopping_item.id == item_id:
                    item = shopping_item
                    break

            if not item or not item.auto_purchase:
                return None

            # Get user preferences
            preferences = self.shopping_preferences.get(user_id)
            if not preferences:
                logger.warning(f"No shopping preferences found for user {user_id}")
                return None

            # Find best product
            products = await self.search_products(
                item.product_name,
                item.category,
                item.max_price,
                preferences.preferred_platforms
            )

            if not products:
                logger.warning(f"No products found for {item.product_name}")
                return None

            # Select best product based on preferences
            best_product = await self._select_best_product(products, preferences)

            # Create order
            order_id = await self._create_order(user_id, [best_product], item.quantity, preferences)

            if order_id:
                # Remove item from shopping list
                self.shopping_lists[user_id].remove(item)
                logger.info(f"Auto-purchased {item.product_name} for user {user_id}")

            return order_id

        except Exception as e:
            logger.error(f"Auto purchase error: {e}")
            return None

    async def _select_best_product(self, products: List[Product], preferences: ShoppingPreferences) -> Product:
        """Select best product based on user preferences"""
        try:
            # Score products based on preferences
            scored_products = []

            for product in products:
                score = 0

                # Brand preference
                if product.brand in preferences.preferred_brands:
                    score += 20

                # Platform preference
                if product.platform in preferences.preferred_platforms:
                    score += 15

                # Quality preference
                if preferences.quality_preference == "premium" and product.rating >= 4.5:
                    score += 25
                elif preferences.quality_preference == "balanced" and product.rating >= 4.0:
                    score += 20
                elif preferences.quality_preference == "budget":
                    score += 30 - (product.price / 100)  # Lower price = higher score

                # Price consideration
                category_budget = preferences.budget_limits.get(product.category, float('inf'))
                if product.price <= category_budget:
                    score += 10

                scored_products.append((product, score))

            # Return product with highest score
            best_product = max(scored_products, key=lambda x: x[1])[0]
            return best_product

        except Exception as e:
            logger.error(f"Product selection error: {e}")
            return products[0] if products else None

    async def _create_order(self, user_id: str, products: List[Product], quantity: int,
                          preferences: ShoppingPreferences) -> Optional[str]:
        """Create order for products"""
        try:
            order_id = f"order_{int(time.time())}_{hash(user_id)}"

            # Calculate total amount
            total_amount = sum(product.price * quantity for product in products)

            # Select payment method
            payment_method = preferences.payment_methods[0] if preferences.payment_methods else "stripe"

            order = Order(
                id=order_id,
                user_id=user_id,
                platform=products[0].platform,
                products=[{"product": asdict(p), "quantity": quantity} for p in products],
                total_amount=total_amount,
                currency="USD",
                status=OrderStatus.PENDING,
                tracking_number=None,
                estimated_delivery=None,
                actual_delivery=None,
                payment_method=payment_method,
                shipping_address={},  # Would be loaded from user profile
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

            # Process payment
            payment_success = await self._process_payment(order, payment_method)

            if payment_success:
                order.status = OrderStatus.CONFIRMED
                self.orders.append(order)

                # Store in database
                await redis_client.lpush(f"orders:{user_id}", asdict(order))

                logger.info(f"Created order {order_id} for user {user_id}")
                return order_id

            return None

        except Exception as e:
            logger.error(f"Order creation error: {e}")
            return None

    async def _process_payment(self, order: Order, payment_method: str) -> bool:
        """Process payment for order"""
        try:
            if payment_method == "stripe" and "stripe" in self.payment_processors:
                # Process Stripe payment
                return await self._process_stripe_payment(order)
            elif payment_method == "paypal" and "paypal" in self.payment_processors:
                # Process PayPal payment
                return await self._process_paypal_payment(order)
            else:
                # Mock payment processing
                logger.info(f"Mock payment processed for order {order.id}")
                return True

        except Exception as e:
            logger.error(f"Payment processing error: {e}")
            return False

    async def _process_stripe_payment(self, order: Order) -> bool:
        """Process Stripe payment"""
        try:
            # This would integrate with actual Stripe API
            logger.info(f"Processing Stripe payment for order {order.id}")
            return True
        except Exception as e:
            logger.error(f"Stripe payment error: {e}")
            return False

    async def _process_paypal_payment(self, order: Order) -> bool:
        """Process PayPal payment"""
        try:
            # This would integrate with actual PayPal API
            logger.info(f"Processing PayPal payment for order {order.id}")
            return True
        except Exception as e:
            logger.error(f"PayPal payment error: {e}")
            return False

    async def set_price_alert(self, user_id: str, product_id: str, target_price: float) -> str:
        """Set price alert for product"""
        try:
            alert_id = f"alert_{int(time.time())}_{hash(product_id)}"

            # Get current price
            product = self.products.get(product_id)
            current_price = product.price if product else 0.0

            alert = PriceAlert(
                id=alert_id,
                user_id=user_id,
                product_id=product_id,
                target_price=target_price,
                current_price=current_price,
                price_drop_percentage=0.0,
                alert_triggered=False,
                created_at=datetime.now()
            )

            self.price_alerts.append(alert)

            # Store in database
            await redis_client.lpush(f"price_alerts:{user_id}", asdict(alert))

            logger.info(f"Set price alert for product {product_id} at ${target_price}")
            return alert_id

        except Exception as e:
            logger.error(f"Price alert creation error: {e}")
            return ""

    async def start_monitoring(self):
        """Start monitoring systems"""
        try:
            if self.monitoring_active:
                return

            self.monitoring_active = True

            # Start monitoring tasks
            self.monitoring_tasks = [
                asyncio.create_task(self._monitor_prices()),
                asyncio.create_task(self._monitor_inventory()),
                asyncio.create_task(self._monitor_deals()),
                asyncio.create_task(self._process_auto_purchases()),
            ]

            logger.info("✅ Shopping monitoring started")

        except Exception as e:
            logger.error(f"Monitoring start error: {e}")

    async def _monitor_prices(self):
        """Monitor product prices for alerts"""
        while self.monitoring_active:
            try:
                for alert in self.price_alerts:
                    if not alert.alert_triggered:
                        # Check current price
                        current_price = await self._get_current_price(alert.product_id)

                        if current_price and current_price <= alert.target_price:
                            alert.alert_triggered = True
                            alert.price_drop_percentage = ((alert.current_price - current_price) / alert.current_price) * 100

                            # Send notification
                            await self._send_price_alert_notification(alert)

                await asyncio.sleep(300)  # Check every 5 minutes

            except Exception as e:
                logger.error(f"Price monitoring error: {e}")
                await asyncio.sleep(300)

    async def _monitor_inventory(self):
        """Monitor inventory levels for auto-reordering"""
        while self.monitoring_active:
            try:
                if self.inventory_manager:
                    await self.inventory_manager.check_inventory_levels()

                await asyncio.sleep(3600)  # Check every hour

            except Exception as e:
                logger.error(f"Inventory monitoring error: {e}")
                await asyncio.sleep(3600)

    async def _monitor_deals(self):
        """Monitor for deals and discounts"""
        while self.monitoring_active:
            try:
                if self.deal_finder:
                    deals = await self.deal_finder.find_deals()

                    for deal in deals:
                        await self._notify_deal(deal)

                await asyncio.sleep(1800)  # Check every 30 minutes

            except Exception as e:
                logger.error(f"Deal monitoring error: {e}")
                await asyncio.sleep(1800)

    async def _process_auto_purchases(self):
        """Process automatic purchases"""
        while self.monitoring_active:
            try:
                for user_id, shopping_list in self.shopping_lists.items():
                    for item in shopping_list:
                        if item.auto_purchase and item.target_date and item.target_date <= datetime.now():
                            await self.auto_purchase_item(user_id, item.id)

                await asyncio.sleep(3600)  # Check every hour

            except Exception as e:
                logger.error(f"Auto purchase processing error: {e}")
                await asyncio.sleep(3600)

    def _deduplicate_products(self, products: List[Product]) -> List[Product]:
        """Remove duplicate products"""
        seen = set()
        unique_products = []

        for product in products:
            # Create a key based on name and brand
            key = f"{product.name.lower()}_{product.brand.lower()}"
            if key not in seen:
                seen.add(key)
                unique_products.append(product)

        return unique_products

    def _sort_products_by_relevance(self, products: List[Product], query: str) -> List[Product]:
        """Sort products by relevance to search query"""
        try:
            # Simple relevance scoring
            scored_products = []
            query_lower = query.lower()

            for product in products:
                score = 0

                # Name match
                if query_lower in product.name.lower():
                    score += 10

                # Brand match
                if query_lower in product.brand.lower():
                    score += 5

                # Description match
                if query_lower in product.description.lower():
                    score += 3

                # Rating bonus
                score += product.rating

                # Availability bonus
                if product.availability:
                    score += 2

                scored_products.append((product, score))

            # Sort by score descending
            scored_products.sort(key=lambda x: x[1], reverse=True)

            return [product for product, score in scored_products]

        except Exception as e:
            logger.error(f"Product sorting error: {e}")
            return products

    async def _get_current_price(self, product_id: str) -> Optional[float]:
        """Get current price for product"""
        try:
            product = self.products.get(product_id)
            if product:
                # Update price from platform
                platform_client = self.platform_clients.get(product.platform)
                if platform_client:
                    updated_product = await platform_client.get_product(product_id)
                    if updated_product:
                        return updated_product.price
            return None
        except Exception as e:
            logger.error(f"Price retrieval error: {e}")
            return None

    async def _send_price_alert_notification(self, alert: PriceAlert):
        """Send price alert notification"""
        try:
            logger.info(f"PRICE ALERT: Product {alert.product_id} dropped to ${alert.current_price} (target: ${alert.target_price})")
        except Exception as e:
            logger.error(f"Price alert notification error: {e}")

    async def _notify_deal(self, deal: Dict[str, Any]):
        """Notify user about deal"""
        try:
            logger.info(f"DEAL ALERT: {deal}")
        except Exception as e:
            logger.error(f"Deal notification error: {e}")

    async def _load_shopping_data(self):
        """Load existing shopping data"""
        try:
            # Load shopping preferences
            pref_keys = await redis_client.keys("shopping_preferences:*")
            for key in pref_keys:
                user_id = key.split(":")[-1]
                pref_data = await redis_client.get(key)
                if pref_data:
                    self.shopping_preferences[user_id] = pref_data

            logger.info(f"✅ Loaded shopping data for {len(self.shopping_preferences)} users")

        except Exception as e:
            logger.error(f"Shopping data loading error: {e}")

    async def stop_monitoring(self):
        """Stop monitoring systems"""
        try:
            self.monitoring_active = False

            for task in self.monitoring_tasks:
                task.cancel()

            await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
            self.monitoring_tasks.clear()

            logger.info("Shopping monitoring stopped")

        except Exception as e:
            logger.error(f"Monitoring stop error: {e}")

    async def cleanup(self):
        """Cleanup shopping service"""
        try:
            await self.stop_monitoring()

            # Clear caches
            self.product_cache.clear()
            self.products.clear()

            logger.info("Shopping service cleanup completed")

        except Exception as e:
            logger.error(f"Shopping cleanup error: {e}")

# Supporting Classes (Placeholder implementations)
class PricePredictionModel:
    async def initialize(self): pass

class ProductRecommendationEngine:
    async def initialize(self): pass

class DemandForecastingModel:
    async def initialize(self): pass

class BudgetOptimizer:
    async def initialize(self): pass

class AmazonClient:
    async def initialize(self): pass
    async def search_products(self, query, category=None, max_price=None, limit=10): return []
    async def get_product(self, product_id): return None

class EbayClient:
    async def initialize(self): pass
    async def search_products(self, query, category=None, max_price=None, limit=10): return []

class WalmartClient:
    async def initialize(self): pass
    async def search_products(self, query, category=None, max_price=None, limit=10): return []

class TargetClient:
    async def initialize(self): pass
    async def search_products(self, query, category=None, max_price=None, limit=10): return []

class BestBuyClient:
    async def initialize(self): pass
    async def search_products(self, query, category=None, max_price=None, limit=10): return []

class PayPalClient:
    async def initialize(self): pass

class AmazonPriceMonitor:
    async def initialize(self): pass

class EbayPriceMonitor:
    async def initialize(self): pass

class WalmartPriceMonitor:
    async def initialize(self): pass

class AutoPurchaseEngine:
    async def initialize(self): pass

class InventoryManager:
    async def initialize(self): pass
    async def check_inventory_levels(self): pass

class DealFinder:
    async def initialize(self): pass
    async def find_deals(self): return []

class CouponManager:
    async def initialize(self): pass

class CashbackTracker:
    async def initialize(self): pass

# Backward compatibility alias
ShoppingService = UltraAdvancedShoppingService
