{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "livekit-client": "^1.3.0", "axios": "^1.6.0", "react-router-dom": "^6.20.0", "@mui/material": "^5.14.0", "@mui/icons-material": "^5.14.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "framer-motion": "^10.16.0", "react-speech-kit": "^3.0.1", "react-webcam": "^7.1.1", "socket.io-client": "^4.7.0", "react-query": "^3.39.0", "zustand": "^4.4.0", "react-hot-toast": "^2.4.0", "lucide-react": "^0.294.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}