"""
JARVIS - Advanced AI Assistant Backend
Main FastAPI application with comprehensive features
"""

import os
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>
from fastapi.staticfiles import StaticFiles
import uvicorn

# Simple logger replacement
class SimpleLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")

logger = SimpleLogger()

# Import routers
from app.routers import (
    auth, ai, voice, vision, automation, device_control,
    smart_home, web_scraping, communication, files,
    workflows, analytics, system_info, healthcare, booking,
    advanced_vision, advanced_nlp
)

# Import core modules
from app.core.database import init_db
from app.core.redis_client import init_redis
from app.core.security import get_current_user
from app.core.config import settings
from app.services.livekit_service import LiveKitService
from app.services.ai_service import AIService
from app.services.voice_service import VoiceService
from app.services.automation_service import AutomationService

# Global services
livekit_service = None
ai_service = None
voice_service = None
automation_service = None

@asynccontextmanager
async def lifespan(_app: FastAPI):
    """Application lifespan manager"""
    global livekit_service, ai_service, voice_service, automation_service
    
    logger.info("🚀 Starting JARVIS Backend...")
    
    # Initialize database
    await init_db()
    logger.info("✅ Database initialized")
    
    # Initialize Redis
    await init_redis()
    logger.info("✅ Redis initialized")
    
    # Initialize services
    livekit_service = LiveKitService()
    ai_service = AIService()
    voice_service = VoiceService()
    automation_service = AutomationService()

    # Initialize all services
    await ai_service.initialize()
    await voice_service.initialize()
    await livekit_service.initialize()
    await automation_service.initialize()
    
    logger.info("✅ All services initialized")
    logger.info("🎯 JARVIS Backend is ready!")
    
    yield
    
    # Cleanup
    logger.info("🔄 Shutting down JARVIS Backend...")
    if livekit_service:
        await livekit_service.cleanup()
    if ai_service:
        await ai_service.cleanup()
    logger.info("✅ Cleanup completed")

# Create FastAPI app
app = FastAPI(
    title="JARVIS - Advanced AI Assistant",
    description="Comprehensive AI assistant with voice, vision, automation, and device control",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Security
security = HTTPBearer()

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure based on your deployment
)

# Static files
os.makedirs("uploads", exist_ok=True)
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(ai.router, prefix="/api/v1/ai", tags=["AI & NLP"])
app.include_router(voice.router, prefix="/api/v1/voice", tags=["Voice Processing"])
app.include_router(vision.router, prefix="/api/v1/vision", tags=["Computer Vision"])
app.include_router(automation.router, prefix="/api/v1/automation", tags=["Automation"])
app.include_router(device_control.router, prefix="/api/v1/device", tags=["Device Control"])
app.include_router(smart_home.router, prefix="/api/v1/smart-home", tags=["Smart Home"])
app.include_router(web_scraping.router, prefix="/api/v1/web", tags=["Web Scraping"])
app.include_router(communication.router, prefix="/api/v1/communication", tags=["Communication"])
app.include_router(files.router, prefix="/api/v1/files", tags=["File Management"])
app.include_router(workflows.router, prefix="/api/v1/workflows", tags=["Workflows"])
app.include_router(analytics.router, prefix="/api/v1/analytics", tags=["Analytics"])
app.include_router(system_info.router, prefix="/api/v1/system", tags=["System Info"])
app.include_router(healthcare.router, prefix="/api/v1/healthcare", tags=["Healthcare & Medical"])
app.include_router(booking.router, prefix="/api/v1/booking", tags=["Booking & Reservations"])
app.include_router(advanced_vision.router, prefix="/api/v1/advanced-vision", tags=["Advanced Computer Vision"])
app.include_router(advanced_nlp.router, prefix="/api/v1/advanced-nlp", tags=["Advanced NLP"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "JARVIS - Advanced AI Assistant Backend",
        "version": "2.0.0",
        "status": "operational",
        "features": [
            "Voice Recognition & Synthesis",
            "Computer Vision",
            "Device Control",
            "Smart Home Integration",
            "Web Scraping",
            "Workflow Automation",
            "Real-time Communication",
            "AI-powered Conversations"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "services": {
            "database": "connected",
            "redis": "connected",
            "ai_service": "ready",
            "livekit": "ready"
        }
    }

@app.post("/api/v1/trigger-workflow/")
async def trigger_workflow(
    request: dict,
    _background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Legacy endpoint for workflow triggering"""
    command = request.get("command", "")
    
    if not command:
        raise HTTPException(status_code=400, detail="Command is required")
    
    # Process command with AI service
    response = await ai_service.process_command(command, current_user)
    
    return {"response": response}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
