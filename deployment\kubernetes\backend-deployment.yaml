apiVersion: apps/v1
kind: Deployment
metadata:
  name: jarvis-backend
  namespace: jarvis
  labels:
    app: jarvis-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: jarvis-backend
  template:
    metadata:
      labels:
        app: jarvis-backend
    spec:
      containers:
      - name: jarvis-backend
        image: jarvis/backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: jarvis-config
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: jarvis-config
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: jarvis-secrets
              key: SECRET_KEY
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: jarvis-secrets
              key: OPENAI_API_KEY
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: jarvis-secrets
              key: ANTHROPIC_API_KEY
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: jarvis-secrets
              key: GOOGLE_API_KEY
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: jarvis-data
          mountPath: /app/data
        - name: jarvis-logs
          mountPath: /app/logs
      volumes:
      - name: jarvis-data
        persistentVolumeClaim:
          claimName: jarvis-data-pvc
      - name: jarvis-logs
        persistentVolumeClaim:
          claimName: jarvis-logs-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: jarvis-backend-service
  namespace: jarvis
spec:
  selector:
    app: jarvis-backend
  ports:
  - protocol: TCP
    port: 8000
    targetPort: 8000
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: jarvis-data-pvc
  namespace: jarvis
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: jarvis-logs-pvc
  namespace: jarvis
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: fast-ssd
