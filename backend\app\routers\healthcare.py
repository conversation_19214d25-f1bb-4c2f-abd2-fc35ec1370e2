"""
Healthcare router for JARVIS
Comprehensive health monitoring, medical assistance, and emergency response
"""

from typing import Dict, List, Any, Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_db, User
from ..core.security import get_current_active_user
from ..services.healthcare_service import HealthcareService
from ..services.emergency_service import EmergencyService

router = APIRouter()

# Global service instances
healthcare_service = HealthcareService()
emergency_service = EmergencyService()

class SymptomAnalysisRequest(BaseModel):
    symptoms: List[str]
    age: int
    gender: str
    severity: Optional[str] = "mild"

class VitalSignsRequest(BaseModel):
    heart_rate: Optional[float] = None
    blood_pressure_systolic: Optional[float] = None
    blood_pressure_diastolic: Optional[float] = None
    temperature: Optional[float] = None
    oxygen_saturation: Optional[float] = None
    respiratory_rate: Optional[float] = None

class MedicationReminderRequest(BaseModel):
    medication: str
    dosage: str
    frequency: str
    start_date: str

class HealthAssessmentRequest(BaseModel):
    age: int
    weight: float
    height: float
    exercise_frequency: int
    smoking: bool
    alcohol_consumption: int
    medical_conditions: List[str]
    medications: List[str]

class EmergencyRequest(BaseModel):
    emergency_type: str
    location: Dict[str, float]
    description: str
    severity: int

class MentalHealthRequest(BaseModel):
    mood: str
    stress_level: int
    anxiety_level: Optional[int] = None
    sleep_quality: Optional[int] = None

@router.on_event("startup")
async def startup_healthcare_services():
    """Initialize healthcare services on startup"""
    await healthcare_service.initialize()
    await emergency_service.initialize()

@router.post("/analyze-symptoms")
async def analyze_symptoms(
    request: SymptomAnalysisRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Analyze symptoms and provide medical insights"""
    
    try:
        analysis = await healthcare_service.analyze_symptoms(
            user_id=str(current_user.id),
            symptoms=request.symptoms,
            age=request.age,
            gender=request.gender
        )
        
        # If high urgency, trigger emergency response in background
        if analysis.get("urgency") == "high":
            background_tasks.add_task(
                emergency_service.trigger_emergency_response,
                str(current_user.id),
                "medical",
                0.9,
                {"symptoms": request.symptoms}
            )
        
        return analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Symptom analysis failed: {str(e)}")

@router.post("/monitor-vitals")
async def monitor_vital_signs(
    request: VitalSignsRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Monitor and analyze vital signs"""
    
    try:
        vitals = {k: v for k, v in request.dict().items() if v is not None}
        
        result = await healthcare_service.monitor_vital_signs(
            user_id=str(current_user.id),
            vitals=vitals
        )
        
        # Check for critical alerts
        if result.get("status") == "abnormal":
            critical_alerts = [alert for alert in result.get("alerts", []) if alert.get("severity") == "high"]
            if critical_alerts:
                background_tasks.add_task(
                    emergency_service.trigger_emergency_response,
                    str(current_user.id),
                    "critical_vitals",
                    0.8,
                    {"vitals": vitals, "alerts": critical_alerts}
                )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Vital signs monitoring failed: {str(e)}")

@router.post("/medication-reminder")
async def set_medication_reminder(
    request: MedicationReminderRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Set up medication reminders"""
    
    try:
        result = await healthcare_service.medication_reminder(
            user_id=str(current_user.id),
            medication=request.medication,
            dosage=request.dosage,
            frequency=request.frequency
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Medication reminder setup failed: {str(e)}")

@router.post("/health-assessment")
async def health_assessment(
    request: HealthAssessmentRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Comprehensive health assessment"""
    
    try:
        assessment_data = request.dict()
        
        result = await healthcare_service.health_assessment(
            user_id=str(current_user.id),
            assessment_data=assessment_data
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health assessment failed: {str(e)}")

@router.post("/emergency-alert")
async def trigger_emergency_alert(
    request: EmergencyRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Trigger emergency alert and response"""
    
    try:
        # Trigger emergency response immediately
        response = await emergency_service.trigger_emergency_response(
            user_id=str(current_user.id),
            emergency_type=request.emergency_type,
            confidence=request.severity / 10.0,
            context={
                "location": request.location,
                "description": request.description,
                "user_triggered": True
            }
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Emergency alert failed: {str(e)}")

@router.post("/mental-health-support")
async def mental_health_support(
    request: MentalHealthRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Provide mental health support and resources"""
    
    try:
        result = await healthcare_service.mental_health_support(
            user_id=str(current_user.id),
            mood=request.mood,
            stress_level=request.stress_level
        )
        
        # Check for crisis intervention
        if request.stress_level >= 9 or request.mood in ["suicidal", "severely_depressed"]:
            background_tasks.add_task(
                emergency_service.trigger_emergency_response,
                str(current_user.id),
                "mental_health_crisis",
                0.95,
                {"mood": request.mood, "stress_level": request.stress_level}
            )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Mental health support failed: {str(e)}")

@router.get("/health-records")
async def get_health_records(
    record_type: Optional[str] = None,
    limit: int = 50,
    current_user: User = Depends(get_current_active_user)
):
    """Get user's health records"""
    
    try:
        # This would integrate with the database to get health records
        # For now, return a placeholder response
        
        return {
            "records": [],
            "total": 0,
            "record_type": record_type,
            "user_id": current_user.id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get health records: {str(e)}")

@router.get("/medication-reminders")
async def get_medication_reminders(
    current_user: User = Depends(get_current_active_user)
):
    """Get active medication reminders"""
    
    try:
        # This would get reminders from Redis/database
        return {
            "reminders": [],
            "total": 0,
            "user_id": current_user.id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get medication reminders: {str(e)}")

@router.get("/emergency-contacts")
async def get_emergency_contacts(
    current_user: User = Depends(get_current_active_user)
):
    """Get user's emergency contacts"""
    
    try:
        contacts = await emergency_service.get_emergency_contacts(str(current_user.id))
        
        return {
            "contacts": contacts,
            "total": len(contacts),
            "user_id": current_user.id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get emergency contacts: {str(e)}")

@router.post("/emergency-contacts")
async def add_emergency_contact(
    contact_data: Dict[str, Any],
    current_user: User = Depends(get_current_active_user)
):
    """Add emergency contact"""
    
    try:
        # This would store the emergency contact
        return {
            "message": "Emergency contact added successfully",
            "contact": contact_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add emergency contact: {str(e)}")

@router.get("/health-insights")
async def get_health_insights(
    current_user: User = Depends(get_current_active_user)
):
    """Get AI-powered health insights and recommendations"""
    
    try:
        # This would analyze user's health data and provide insights
        insights = {
            "overall_health_score": 85,
            "trends": [
                {"metric": "heart_rate", "trend": "stable", "change": 0.02},
                {"metric": "sleep_quality", "trend": "improving", "change": 0.15}
            ],
            "recommendations": [
                "Increase daily water intake",
                "Consider adding 30 minutes of cardio exercise",
                "Maintain consistent sleep schedule"
            ],
            "risk_factors": [
                {"factor": "sedentary_lifestyle", "risk_level": "moderate"}
            ],
            "next_checkup": "2024-03-15"
        }
        
        return insights
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get health insights: {str(e)}")

@router.get("/health-status")
async def get_health_service_status():
    """Get health service status and capabilities"""
    
    return {
        "status": "operational",
        "features": [
            "symptom_analysis",
            "vital_signs_monitoring",
            "medication_reminders",
            "health_assessments",
            "emergency_response",
            "mental_health_support",
            "health_insights"
        ],
        "emergency_services": {
            "available": True,
            "response_time": "< 30 seconds",
            "coverage": "24/7"
        },
        "integrations": [
            "emergency_services",
            "healthcare_providers",
            "pharmacies",
            "mental_health_resources"
        ]
    }
