@echo off
REM JARVIS Deployment Script for Windows
REM Ultra-Advanced AI Assistant with Zero Human Intervention

echo 🚀 Starting JARVIS Deployment...
echo.

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

echo ✅ Docker and Docker Compose are installed

REM Check if .env file exists
if not exist .env (
    echo ⚠️  .env file not found. Creating from template...
    copy .env.example .env
    echo ⚠️  Please edit .env file with your API keys before continuing.
    echo ⚠️  Required keys: OPENAI_API_KEY, SUPABASE_URL, SUPABASE_KEY
    pause
)

echo ✅ .env file found

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist livekit mkdir livekit
if not exist nginx mkdir nginx
if not exist monitoring mkdir monitoring
if not exist database mkdir database

echo ✅ Directories created

REM Create LiveKit configuration
echo 🔧 Creating LiveKit configuration...
(
echo port: 7880
echo bind_addresses:
echo   - ""
echo rtc:
echo   tcp_port: 7881
echo   port_range_start: 50000
echo   port_range_end: 60000
echo   use_external_ip: true
echo redis:
echo   address: redis:6379
echo turn:
echo   enabled: true
echo   domain: localhost
echo   cert_file: ""
echo   key_file: ""
echo   tls_port: 5349
echo   udp_port: 3478
echo keys:
echo   APIKey: devkey
echo   APISecret: secret
echo room:
echo   max_participants: 100
echo   empty_timeout: 300s
) > livekit\livekit.yaml

echo ✅ LiveKit configuration created

REM Create Nginx configuration
echo 🔧 Creating Nginx configuration...
if not exist nginx\ssl mkdir nginx\ssl

(
echo events {
echo     worker_connections 1024;
echo }
echo.
echo http {
echo     upstream backend {
echo         server backend:8000;
echo     }
echo.    
echo     upstream frontend {
echo         server frontend:3000;
echo     }
echo.    
echo     server {
echo         listen 80;
echo         server_name localhost;
echo.        
echo         location / {
echo             proxy_pass http://frontend;
echo             proxy_set_header Host $host;
echo             proxy_set_header X-Real-IP $remote_addr;
echo         }
echo.        
echo         location /api/ {
echo             proxy_pass http://backend;
echo             proxy_set_header Host $host;
echo             proxy_set_header X-Real-IP $remote_addr;
echo         }
echo     }
echo }
) > nginx\nginx.conf

echo ✅ Nginx configuration created

REM Create Prometheus configuration
echo 🔧 Creating Prometheus configuration...
(
echo global:
echo   scrape_interval: 15s
echo.
echo scrape_configs:
echo   - job_name: 'jarvis-backend'
echo     static_configs:
echo       - targets: ['backend:8000']
echo     metrics_path: '/metrics'
) > monitoring\prometheus.yml

echo ✅ Prometheus configuration created

REM Start services
echo 🚀 Building and starting JARVIS services...
docker-compose pull
docker-compose build --no-cache
docker-compose up -d

echo ✅ All services started

REM Wait for services
echo ⏳ Waiting for services to be ready...
timeout /t 30 /nobreak >nul

echo.
echo 🎉 JARVIS is now running!
echo.
echo 📱 Access your services:
echo    • JARVIS Frontend:     http://localhost:3000
echo    • API Documentation:   http://localhost:8000/docs
echo    • N8N Workflows:       http://localhost:5678 (admin/admin123)
echo    • Grafana Monitoring:  http://localhost:3001 (admin/admin123)
echo    • Prometheus Metrics:  http://localhost:9090
echo.
echo 🔧 Admin Access:
echo    • Username: admin
echo    • Password: admin123
echo.
echo ⚠️  Remember to:
echo    1. Update your .env file with real API keys
echo    2. Change default passwords in production
echo    3. Configure SSL certificates for HTTPS
echo.
echo 🎯 Deployment completed successfully!
pause
