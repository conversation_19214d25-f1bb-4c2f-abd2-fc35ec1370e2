"""
Learning and Adaptation Service for JARVIS
Ultra-advanced machine learning and behavioral adaptation
"""

import asyncio
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
import pickle
import base64

# Machine Learning imports
try:
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, mean_squared_error
    import pandas as pd
    from scipy import stats
except ImportError:
    # Fallback for development
    pass

from loguru import logger
from ..core.config import settings
from ..core.redis_client import redis_client

class LearningAdaptationService:
    """Ultra-advanced learning and behavioral adaptation system"""
    
    def __init__(self):
        self.user_models = {}
        self.behavior_patterns = {}
        self.preference_models = {}
        self.prediction_models = {}
        self.adaptation_rules = {}
        self.learning_history = defaultdict(list)
        
    async def initialize(self):
        """Initialize learning and adaptation service"""
        try:
            logger.info("Initializing Learning and Adaptation Service...")
            
            # Initialize base models
            await self.initialize_base_models()
            
            # Load existing user models
            await self.load_user_models()
            
            # Initialize adaptation rules
            await self.initialize_adaptation_rules()
            
            logger.info("🧠 Learning and Adaptation Service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Learning and Adaptation Service: {e}")
    
    async def learn_from_interaction(self, user_id: str, interaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Learn from user interactions and adapt behavior"""
        try:
            learning_result = {
                "timestamp": datetime.utcnow().isoformat(),
                "user_id": user_id,
                "interaction_type": interaction_data.get("type"),
                "learning_updates": [],
                "adaptations_made": []
            }
            
            # Extract features from interaction
            features = await self.extract_interaction_features(interaction_data)
            
            # Update user behavior model
            behavior_update = await self.update_behavior_model(user_id, features)
            learning_result["learning_updates"].append(behavior_update)
            
            # Update preference model
            preference_update = await self.update_preference_model(user_id, interaction_data)
            learning_result["learning_updates"].append(preference_update)
            
            # Update prediction models
            prediction_update = await self.update_prediction_models(user_id, interaction_data)
            learning_result["learning_updates"].append(prediction_update)
            
            # Apply adaptations based on learning
            adaptations = await self.apply_behavioral_adaptations(user_id, features)
            learning_result["adaptations_made"] = adaptations
            
            # Store learning history
            await self.store_learning_history(user_id, learning_result)
            
            return learning_result
            
        except Exception as e:
            logger.error(f"Learning from interaction error: {e}")
            return {"error": str(e)}
    
    async def predict_user_behavior(self, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Predict user behavior based on learned patterns"""
        try:
            # Get user models
            user_model = await self.get_user_model(user_id)
            
            if not user_model:
                return {"predictions": [], "confidence": 0.0}
            
            # Extract context features
            context_features = await self.extract_context_features(context)
            
            predictions = {
                "next_action": await self.predict_next_action(user_id, context_features),
                "preferences": await self.predict_preferences(user_id, context_features),
                "response_time": await self.predict_response_time(user_id, context_features),
                "satisfaction": await self.predict_satisfaction(user_id, context_features),
                "engagement_level": await self.predict_engagement(user_id, context_features)
            }
            
            # Calculate overall confidence
            confidences = [pred.get("confidence", 0.0) for pred in predictions.values()]
            overall_confidence = np.mean(confidences) if confidences else 0.0
            
            return {
                "predictions": predictions,
                "confidence": overall_confidence,
                "context": context,
                "model_version": user_model.get("version", "1.0")
            }
            
        except Exception as e:
            logger.error(f"Behavior prediction error: {e}")
            return {"predictions": [], "confidence": 0.0}
    
    async def adapt_response_style(self, user_id: str, base_response: str, 
                                 context: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt response style based on user preferences and patterns"""
        try:
            # Get user communication preferences
            comm_prefs = await self.get_communication_preferences(user_id)
            
            # Analyze current context
            context_analysis = await self.analyze_communication_context(context)
            
            # Adapt response based on learned preferences
            adapted_response = await self.apply_communication_adaptations(
                base_response, comm_prefs, context_analysis
            )
            
            return {
                "original_response": base_response,
                "adapted_response": adapted_response,
                "adaptations_applied": comm_prefs,
                "context_factors": context_analysis
            }
            
        except Exception as e:
            logger.error(f"Response adaptation error: {e}")
            return {"adapted_response": base_response}
    
    async def learn_user_patterns(self, user_id: str, pattern_type: str, 
                                data_points: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Learn specific user patterns from data points"""
        try:
            if len(data_points) < 5:
                return {"message": "Insufficient data for pattern learning", "patterns": []}
            
            patterns_learned = []
            
            if pattern_type == "temporal":
                temporal_patterns = await self.learn_temporal_patterns(data_points)
                patterns_learned.extend(temporal_patterns)
                
            elif pattern_type == "behavioral":
                behavioral_patterns = await self.learn_behavioral_patterns(data_points)
                patterns_learned.extend(behavioral_patterns)
                
            elif pattern_type == "preference":
                preference_patterns = await self.learn_preference_patterns(data_points)
                patterns_learned.extend(preference_patterns)
                
            elif pattern_type == "usage":
                usage_patterns = await self.learn_usage_patterns(data_points)
                patterns_learned.extend(usage_patterns)
            
            # Store learned patterns
            await self.store_user_patterns(user_id, pattern_type, patterns_learned)
            
            return {
                "pattern_type": pattern_type,
                "patterns_learned": patterns_learned,
                "data_points_analyzed": len(data_points),
                "confidence": self.calculate_pattern_confidence(patterns_learned)
            }
            
        except Exception as e:
            logger.error(f"Pattern learning error: {e}")
            return {"error": str(e)}
    
    async def personalize_interface(self, user_id: str, interface_type: str) -> Dict[str, Any]:
        """Personalize interface based on user behavior and preferences"""
        try:
            # Get user interface preferences
            ui_prefs = await self.get_interface_preferences(user_id)
            
            # Get usage patterns
            usage_patterns = await self.get_usage_patterns(user_id)
            
            # Generate personalization recommendations
            personalizations = {
                "layout": await self.recommend_layout(ui_prefs, usage_patterns),
                "colors": await self.recommend_colors(ui_prefs),
                "shortcuts": await self.recommend_shortcuts(usage_patterns),
                "widgets": await self.recommend_widgets(usage_patterns),
                "notifications": await self.recommend_notification_settings(ui_prefs, usage_patterns)
            }
            
            return {
                "interface_type": interface_type,
                "personalizations": personalizations,
                "user_preferences": ui_prefs,
                "usage_patterns": usage_patterns
            }
            
        except Exception as e:
            logger.error(f"Interface personalization error: {e}")
            return {"personalizations": {}}
    
    async def adaptive_learning_rate(self, user_id: str, learning_context: Dict[str, Any]) -> float:
        """Calculate adaptive learning rate based on user behavior"""
        try:
            # Get user learning history
            learning_history = await self.get_learning_history(user_id)
            
            # Calculate base learning rate
            base_rate = 0.1
            
            # Adjust based on user engagement
            engagement_factor = learning_context.get("engagement_level", 0.5)
            
            # Adjust based on accuracy of past predictions
            accuracy_history = [h.get("accuracy", 0.5) for h in learning_history[-10:]]
            avg_accuracy = np.mean(accuracy_history) if accuracy_history else 0.5
            
            # Adjust based on interaction frequency
            interaction_frequency = learning_context.get("interaction_frequency", 1.0)
            
            # Calculate adaptive rate
            adaptive_rate = base_rate * engagement_factor * avg_accuracy * interaction_frequency
            
            # Clamp between min and max values
            adaptive_rate = max(0.01, min(0.5, adaptive_rate))
            
            return adaptive_rate
            
        except Exception as e:
            logger.error(f"Adaptive learning rate calculation error: {e}")
            return 0.1
    
    async def extract_interaction_features(self, interaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features from user interaction data"""
        try:
            features = {
                "timestamp": datetime.utcnow().timestamp(),
                "interaction_type": interaction_data.get("type", "unknown"),
                "duration": interaction_data.get("duration", 0),
                "success": interaction_data.get("success", True),
                "user_satisfaction": interaction_data.get("satisfaction", 0.5),
                "context": interaction_data.get("context", {}),
                "response_time": interaction_data.get("response_time", 0),
                "error_count": interaction_data.get("errors", 0),
                "complexity": interaction_data.get("complexity", "medium")
            }
            
            # Add temporal features
            now = datetime.utcnow()
            features.update({
                "hour_of_day": now.hour,
                "day_of_week": now.weekday(),
                "is_weekend": now.weekday() >= 5,
                "month": now.month,
                "season": self.get_season(now.month)
            })
            
            # Add derived features
            features["efficiency"] = features["success"] / max(features["duration"], 1)
            features["error_rate"] = features["error_count"] / max(features["duration"], 1)
            
            return features
            
        except Exception as e:
            logger.error(f"Feature extraction error: {e}")
            return {}
    
    async def update_behavior_model(self, user_id: str, features: Dict[str, Any]) -> Dict[str, Any]:
        """Update user behavior model with new features"""
        try:
            # Get existing model
            model_key = f"behavior_model:{user_id}"
            model_data = await redis_client.get(model_key, {})
            
            # Initialize if new user
            if not model_data:
                model_data = {
                    "feature_history": [],
                    "patterns": {},
                    "last_updated": datetime.utcnow().isoformat(),
                    "version": "1.0"
                }
            
            # Add new features to history
            model_data["feature_history"].append(features)
            
            # Keep only recent history (last 1000 interactions)
            if len(model_data["feature_history"]) > 1000:
                model_data["feature_history"] = model_data["feature_history"][-1000:]
            
            # Update patterns
            if len(model_data["feature_history"]) >= 10:
                patterns = await self.analyze_behavior_patterns(model_data["feature_history"])
                model_data["patterns"] = patterns
            
            model_data["last_updated"] = datetime.utcnow().isoformat()
            
            # Store updated model
            await redis_client.set(model_key, model_data)
            
            return {
                "model_type": "behavior",
                "features_added": 1,
                "total_features": len(model_data["feature_history"]),
                "patterns_detected": len(model_data["patterns"])
            }
            
        except Exception as e:
            logger.error(f"Behavior model update error: {e}")
            return {"error": str(e)}
    
    async def analyze_behavior_patterns(self, feature_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze behavior patterns from feature history"""
        try:
            patterns = {}
            
            # Temporal patterns
            hours = [f["hour_of_day"] for f in feature_history if "hour_of_day" in f]
            if hours:
                patterns["peak_hours"] = self.find_peak_hours(hours)
                patterns["activity_distribution"] = self.calculate_activity_distribution(hours)
            
            # Success patterns
            successes = [f["success"] for f in feature_history if "success" in f]
            if successes:
                patterns["success_rate"] = np.mean(successes)
                patterns["success_trend"] = self.calculate_trend(successes)
            
            # Efficiency patterns
            efficiencies = [f["efficiency"] for f in feature_history if "efficiency" in f]
            if efficiencies:
                patterns["avg_efficiency"] = np.mean(efficiencies)
                patterns["efficiency_trend"] = self.calculate_trend(efficiencies)
            
            # Interaction type preferences
            interaction_types = [f["interaction_type"] for f in feature_history if "interaction_type" in f]
            if interaction_types:
                type_counts = {}
                for itype in interaction_types:
                    type_counts[itype] = type_counts.get(itype, 0) + 1
                patterns["preferred_interactions"] = sorted(type_counts.items(), key=lambda x: x[1], reverse=True)
            
            return patterns
            
        except Exception as e:
            logger.error(f"Pattern analysis error: {e}")
            return {}
    
    def find_peak_hours(self, hours: List[int]) -> List[int]:
        """Find peak activity hours"""
        try:
            hour_counts = {}
            for hour in hours:
                hour_counts[hour] = hour_counts.get(hour, 0) + 1
            
            # Find hours with above-average activity
            avg_activity = np.mean(list(hour_counts.values()))
            peak_hours = [hour for hour, count in hour_counts.items() if count > avg_activity]
            
            return sorted(peak_hours)
        except:
            return []
    
    def calculate_activity_distribution(self, hours: List[int]) -> Dict[str, float]:
        """Calculate activity distribution across time periods"""
        try:
            periods = {
                "morning": 0,    # 6-12
                "afternoon": 0,  # 12-18
                "evening": 0,    # 18-22
                "night": 0       # 22-6
            }
            
            for hour in hours:
                if 6 <= hour < 12:
                    periods["morning"] += 1
                elif 12 <= hour < 18:
                    periods["afternoon"] += 1
                elif 18 <= hour < 22:
                    periods["evening"] += 1
                else:
                    periods["night"] += 1
            
            total = sum(periods.values())
            if total > 0:
                for period in periods:
                    periods[period] = periods[period] / total
            
            return periods
        except:
            return {}
    
    def calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction from values"""
        try:
            if len(values) < 2:
                return "stable"
            
            # Simple linear regression
            x = np.arange(len(values))
            slope, _, _, _, _ = stats.linregress(x, values)
            
            if slope > 0.01:
                return "improving"
            elif slope < -0.01:
                return "declining"
            else:
                return "stable"
        except:
            return "stable"
    
    def get_season(self, month: int) -> str:
        """Get season from month"""
        if month in [12, 1, 2]:
            return "winter"
        elif month in [3, 4, 5]:
            return "spring"
        elif month in [6, 7, 8]:
            return "summer"
        else:
            return "fall"
    
    def calculate_pattern_confidence(self, patterns: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for learned patterns"""
        try:
            if not patterns:
                return 0.0
            
            # Simple confidence calculation based on pattern consistency
            confidence_scores = []
            for pattern in patterns:
                # This would be more sophisticated in a real implementation
                confidence_scores.append(pattern.get("confidence", 0.5))
            
            return np.mean(confidence_scores)
        except:
            return 0.5
    
    async def initialize_base_models(self):
        """Initialize base machine learning models"""
        try:
            # Initialize classification models
            self.preference_models["classifier"] = RandomForestClassifier(n_estimators=100)
            
            # Initialize regression models
            self.prediction_models["regressor"] = GradientBoostingRegressor(n_estimators=100)
            
            # Initialize clustering models
            self.behavior_patterns["clusterer"] = KMeans(n_clusters=5)
            
            # Initialize scalers
            self.prediction_models["scaler"] = StandardScaler()
            
            logger.info("Base ML models initialized")
        except Exception as e:
            logger.error(f"Base model initialization error: {e}")
    
    async def load_user_models(self):
        """Load existing user models from storage"""
        try:
            # This would load pre-trained models from persistent storage
            logger.info("User models loaded")
        except Exception as e:
            logger.error(f"User model loading error: {e}")
    
    async def initialize_adaptation_rules(self):
        """Initialize behavioral adaptation rules"""
        try:
            self.adaptation_rules = {
                "response_style": {
                    "formal": {"conditions": ["business_context", "first_interaction"]},
                    "casual": {"conditions": ["frequent_user", "informal_context"]},
                    "technical": {"conditions": ["technical_query", "expert_user"]}
                },
                "interaction_speed": {
                    "fast": {"conditions": ["high_efficiency_user", "simple_query"]},
                    "detailed": {"conditions": ["learning_user", "complex_query"]}
                }
            }
            logger.info("Adaptation rules initialized")
        except Exception as e:
            logger.error(f"Adaptation rules initialization error: {e}")
    
    async def cleanup(self):
        """Cleanup learning and adaptation service"""
        logger.info("Learning and Adaptation Service cleanup completed")
