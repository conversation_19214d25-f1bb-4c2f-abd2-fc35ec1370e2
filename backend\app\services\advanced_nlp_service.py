"""
Advanced Natural Language Processing Service for JARVIS
Ultra-advanced text understanding, generation, and language intelligence
"""

import asyncio
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import spacy
import nltk
from textblob import TextBlob
from transformers import pipeline, AutoTokenizer, AutoModel
import openai
from sentence_transformers import SentenceTransformer

from loguru import logger
from ..core.config import settings
from ..core.redis_client import redis_client

class AdvancedNLPService:
    """Ultra-advanced natural language processing and understanding"""
    
    def __init__(self):
        self.nlp_model = None
        self.sentiment_analyzer = None
        self.emotion_analyzer = None
        self.intent_classifier = None
        self.entity_extractor = None
        self.text_summarizer = None
        self.question_answerer = None
        self.text_generator = None
        self.sentence_transformer = None
        self.language_detector = None
        self.translator = None
        
    async def initialize(self):
        """Initialize all NLP models and services"""
        try:
            logger.info("Initializing Advanced NLP Service...")
            
            # Download required NLTK data
            try:
                nltk.download('punkt', quiet=True)
                nltk.download('stopwords', quiet=True)
                nltk.download('vader_lexicon', quiet=True)
                nltk.download('wordnet', quiet=True)
            except:
                pass
            
            # Initialize spaCy model
            try:
                self.nlp_model = spacy.load("en_core_web_sm")
                logger.info("✅ spaCy model loaded")
            except:
                logger.warning("⚠️ spaCy model not available")
            
            # Initialize Transformers pipelines
            try:
                self.sentiment_analyzer = pipeline("sentiment-analysis", 
                                                 model="cardiffnlp/twitter-roberta-base-sentiment-latest")
                self.emotion_analyzer = pipeline("text-classification", 
                                                model="j-hartmann/emotion-english-distilroberta-base")
                self.text_summarizer = pipeline("summarization", 
                                               model="facebook/bart-large-cnn")
                self.question_answerer = pipeline("question-answering", 
                                                 model="deepset/roberta-base-squad2")
                self.text_generator = pipeline("text-generation", 
                                              model="gpt2-medium")
                logger.info("✅ Transformers pipelines loaded")
            except Exception as e:
                logger.warning(f"⚠️ Some Transformers models not available: {e}")
            
            # Initialize sentence transformer for embeddings
            try:
                self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
                logger.info("✅ Sentence transformer loaded")
            except:
                logger.warning("⚠️ Sentence transformer not available")
            
            # Initialize language detection
            try:
                self.language_detector = pipeline("text-classification", 
                                                 model="papluca/xlm-roberta-base-language-detection")
                logger.info("✅ Language detector loaded")
            except:
                logger.warning("⚠️ Language detector not available")
            
            logger.info("🚀 Advanced NLP Service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Advanced NLP Service: {e}")
    
    async def analyze_text_comprehensive(self, text: str, analysis_type: str = "full") -> Dict[str, Any]:
        """Comprehensive text analysis with all available NLP models"""
        try:
            analysis_result = {
                "timestamp": datetime.utcnow().isoformat(),
                "text": text,
                "text_length": len(text),
                "word_count": len(text.split())
            }
            
            # Language Detection
            if analysis_type in ["full", "language"]:
                language_data = await self.detect_language(text)
                analysis_result["language"] = language_data
            
            # Sentiment Analysis
            if analysis_type in ["full", "sentiment"]:
                sentiment_data = await self.analyze_sentiment_advanced(text)
                analysis_result["sentiment"] = sentiment_data
            
            # Emotion Analysis
            if analysis_type in ["full", "emotion"]:
                emotion_data = await self.analyze_emotions(text)
                analysis_result["emotions"] = emotion_data
            
            # Intent Classification
            if analysis_type in ["full", "intent"]:
                intent_data = await self.classify_intent_advanced(text)
                analysis_result["intent"] = intent_data
            
            # Named Entity Recognition
            if analysis_type in ["full", "entities"]:
                entities_data = await self.extract_entities_advanced(text)
                analysis_result["entities"] = entities_data
            
            # Key Phrases and Topics
            if analysis_type in ["full", "topics"]:
                topics_data = await self.extract_topics_and_phrases(text)
                analysis_result["topics"] = topics_data
            
            # Text Complexity Analysis
            if analysis_type in ["full", "complexity"]:
                complexity_data = await self.analyze_text_complexity(text)
                analysis_result["complexity"] = complexity_data
            
            # Semantic Similarity and Embeddings
            if analysis_type in ["full", "embeddings"]:
                embeddings_data = await self.generate_embeddings(text)
                analysis_result["embeddings"] = embeddings_data
            
            # Grammar and Style Analysis
            if analysis_type in ["full", "grammar"]:
                grammar_data = await self.analyze_grammar_and_style(text)
                analysis_result["grammar"] = grammar_data
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Comprehensive text analysis error: {e}")
            return {"error": str(e)}
    
    async def detect_language(self, text: str) -> Dict[str, Any]:
        """Detect language of the text"""
        try:
            if self.language_detector and len(text) > 10:
                result = self.language_detector(text)
                return {
                    "language": result[0]["label"],
                    "confidence": result[0]["score"],
                    "method": "transformer"
                }
            else:
                # Fallback to TextBlob
                blob = TextBlob(text)
                detected_lang = blob.detect_language()
                return {
                    "language": detected_lang,
                    "confidence": 0.8,
                    "method": "textblob"
                }
        except Exception as e:
            logger.error(f"Language detection error: {e}")
            return {"language": "en", "confidence": 0.5, "method": "default"}
    
    async def analyze_sentiment_advanced(self, text: str) -> Dict[str, Any]:
        """Advanced sentiment analysis with multiple models"""
        try:
            sentiment_results = {}
            
            # Transformer-based sentiment
            if self.sentiment_analyzer:
                transformer_result = self.sentiment_analyzer(text)
                sentiment_results["transformer"] = {
                    "label": transformer_result[0]["label"],
                    "score": transformer_result[0]["score"]
                }
            
            # TextBlob sentiment
            blob = TextBlob(text)
            sentiment_results["textblob"] = {
                "polarity": blob.sentiment.polarity,
                "subjectivity": blob.sentiment.subjectivity
            }
            
            # VADER sentiment (NLTK)
            try:
                from nltk.sentiment import SentimentIntensityAnalyzer
                sia = SentimentIntensityAnalyzer()
                vader_scores = sia.polarity_scores(text)
                sentiment_results["vader"] = vader_scores
            except:
                pass
            
            # Aggregate sentiment
            overall_sentiment = self.aggregate_sentiment_scores(sentiment_results)
            sentiment_results["overall"] = overall_sentiment
            
            return sentiment_results
            
        except Exception as e:
            logger.error(f"Advanced sentiment analysis error: {e}")
            return {"overall": {"sentiment": "neutral", "confidence": 0.5}}
    
    async def analyze_emotions(self, text: str) -> Dict[str, Any]:
        """Analyze emotions in text"""
        try:
            if not self.emotion_analyzer:
                return {"emotions": []}
            
            results = self.emotion_analyzer(text)
            
            emotions = []
            for result in results:
                emotions.append({
                    "emotion": result["label"],
                    "confidence": result["score"]
                })
            
            # Sort by confidence
            emotions.sort(key=lambda x: x["confidence"], reverse=True)
            
            return {
                "emotions": emotions,
                "primary_emotion": emotions[0] if emotions else {"emotion": "neutral", "confidence": 0.5}
            }
            
        except Exception as e:
            logger.error(f"Emotion analysis error: {e}")
            return {"emotions": []}
    
    async def classify_intent_advanced(self, text: str) -> Dict[str, Any]:
        """Advanced intent classification with context understanding"""
        try:
            # Define intent patterns and keywords
            intent_patterns = {
                "booking": ["book", "reserve", "schedule", "appointment", "reservation"],
                "information": ["what", "how", "when", "where", "why", "tell me", "explain"],
                "control": ["turn on", "turn off", "set", "adjust", "control", "change"],
                "emergency": ["help", "emergency", "urgent", "call", "911", "ambulance"],
                "shopping": ["buy", "purchase", "order", "shop", "add to cart"],
                "navigation": ["directions", "navigate", "route", "go to", "find location"],
                "entertainment": ["play", "music", "movie", "video", "game", "fun"],
                "weather": ["weather", "temperature", "forecast", "rain", "sunny"],
                "reminder": ["remind", "reminder", "alert", "notify", "don't forget"],
                "greeting": ["hello", "hi", "good morning", "good evening", "hey"],
                "goodbye": ["bye", "goodbye", "see you", "farewell", "exit"],
                "complaint": ["problem", "issue", "wrong", "error", "not working"],
                "compliment": ["good", "great", "excellent", "amazing", "wonderful"],
                "question": ["?", "what", "how", "when", "where", "why", "can you"],
                "command": ["please", "do", "make", "create", "generate", "show me"]
            }
            
            text_lower = text.lower()
            intent_scores = {}
            
            # Calculate scores for each intent
            for intent, keywords in intent_patterns.items():
                score = 0
                for keyword in keywords:
                    if keyword in text_lower:
                        score += 1
                
                # Normalize score
                if keywords:
                    intent_scores[intent] = score / len(keywords)
            
            # Find best matching intent
            if intent_scores:
                best_intent = max(intent_scores, key=intent_scores.get)
                confidence = intent_scores[best_intent]
            else:
                best_intent = "general"
                confidence = 0.5
            
            # Extract entities related to the intent
            entities = await self.extract_intent_entities(text, best_intent)
            
            return {
                "intent": best_intent,
                "confidence": confidence,
                "all_scores": intent_scores,
                "entities": entities,
                "context": await self.analyze_context(text)
            }
            
        except Exception as e:
            logger.error(f"Intent classification error: {e}")
            return {"intent": "general", "confidence": 0.5}
    
    async def extract_entities_advanced(self, text: str) -> Dict[str, Any]:
        """Advanced named entity recognition"""
        try:
            entities = {
                "spacy_entities": [],
                "custom_entities": [],
                "dates": [],
                "times": [],
                "numbers": [],
                "emails": [],
                "phones": [],
                "urls": []
            }
            
            # spaCy NER
            if self.nlp_model:
                doc = self.nlp_model(text)
                for ent in doc.ents:
                    entities["spacy_entities"].append({
                        "text": ent.text,
                        "label": ent.label_,
                        "start": ent.start_char,
                        "end": ent.end_char,
                        "description": spacy.explain(ent.label_)
                    })
            
            # Custom regex patterns for specific entities
            patterns = {
                "emails": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
                "phones": r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
                "urls": r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
                "dates": r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',
                "times": r'\b\d{1,2}:\d{2}(?::\d{2})?\s?(?:AM|PM|am|pm)?\b',
                "numbers": r'\b\d+(?:\.\d+)?\b'
            }
            
            for entity_type, pattern in patterns.items():
                matches = re.finditer(pattern, text)
                for match in matches:
                    entities[entity_type].append({
                        "text": match.group(),
                        "start": match.start(),
                        "end": match.end()
                    })
            
            return entities
            
        except Exception as e:
            logger.error(f"Entity extraction error: {e}")
            return {"spacy_entities": [], "custom_entities": []}
    
    async def extract_topics_and_phrases(self, text: str) -> Dict[str, Any]:
        """Extract key topics and phrases from text"""
        try:
            topics_data = {
                "key_phrases": [],
                "topics": [],
                "keywords": [],
                "noun_phrases": []
            }
            
            # Use spaCy for linguistic analysis
            if self.nlp_model:
                doc = self.nlp_model(text)
                
                # Extract noun phrases
                for chunk in doc.noun_chunks:
                    topics_data["noun_phrases"].append({
                        "text": chunk.text,
                        "root": chunk.root.text,
                        "pos": chunk.root.pos_
                    })
                
                # Extract keywords (important words)
                keywords = []
                for token in doc:
                    if (token.pos_ in ["NOUN", "ADJ", "VERB"] and 
                        not token.is_stop and 
                        not token.is_punct and 
                        len(token.text) > 2):
                        keywords.append({
                            "text": token.text,
                            "lemma": token.lemma_,
                            "pos": token.pos_
                        })
                
                topics_data["keywords"] = keywords
            
            # Use TextBlob for additional analysis
            blob = TextBlob(text)
            
            # Extract noun phrases using TextBlob
            textblob_phrases = list(blob.noun_phrases)
            topics_data["key_phrases"] = textblob_phrases
            
            return topics_data
            
        except Exception as e:
            logger.error(f"Topic extraction error: {e}")
            return {"key_phrases": [], "topics": []}
    
    async def analyze_text_complexity(self, text: str) -> Dict[str, Any]:
        """Analyze text complexity and readability"""
        try:
            # Basic metrics
            sentences = text.split('.')
            words = text.split()
            characters = len(text)
            
            avg_sentence_length = len(words) / max(len(sentences), 1)
            avg_word_length = sum(len(word) for word in words) / max(len(words), 1)
            
            # Syllable counting (simplified)
            def count_syllables(word):
                word = word.lower()
                vowels = "aeiouy"
                syllable_count = 0
                prev_char_was_vowel = False
                
                for char in word:
                    if char in vowels:
                        if not prev_char_was_vowel:
                            syllable_count += 1
                        prev_char_was_vowel = True
                    else:
                        prev_char_was_vowel = False
                
                if word.endswith('e'):
                    syllable_count -= 1
                
                return max(1, syllable_count)
            
            total_syllables = sum(count_syllables(word) for word in words)
            avg_syllables_per_word = total_syllables / max(len(words), 1)
            
            # Flesch Reading Ease Score
            if len(sentences) > 0 and len(words) > 0:
                flesch_score = (206.835 - 
                              (1.015 * avg_sentence_length) - 
                              (84.6 * avg_syllables_per_word))
            else:
                flesch_score = 0
            
            # Determine reading level
            if flesch_score >= 90:
                reading_level = "Very Easy"
            elif flesch_score >= 80:
                reading_level = "Easy"
            elif flesch_score >= 70:
                reading_level = "Fairly Easy"
            elif flesch_score >= 60:
                reading_level = "Standard"
            elif flesch_score >= 50:
                reading_level = "Fairly Difficult"
            elif flesch_score >= 30:
                reading_level = "Difficult"
            else:
                reading_level = "Very Difficult"
            
            return {
                "flesch_score": flesch_score,
                "reading_level": reading_level,
                "avg_sentence_length": avg_sentence_length,
                "avg_word_length": avg_word_length,
                "avg_syllables_per_word": avg_syllables_per_word,
                "total_sentences": len(sentences),
                "total_words": len(words),
                "total_characters": characters
            }
            
        except Exception as e:
            logger.error(f"Text complexity analysis error: {e}")
            return {"reading_level": "Unknown"}
    
    async def generate_embeddings(self, text: str) -> Dict[str, Any]:
        """Generate semantic embeddings for text"""
        try:
            if not self.sentence_transformer:
                return {"embeddings": []}
            
            # Generate embeddings
            embeddings = self.sentence_transformer.encode([text])
            
            return {
                "embeddings": embeddings[0].tolist(),
                "dimension": len(embeddings[0]),
                "model": "all-MiniLM-L6-v2"
            }
            
        except Exception as e:
            logger.error(f"Embeddings generation error: {e}")
            return {"embeddings": []}
    
    async def analyze_grammar_and_style(self, text: str) -> Dict[str, Any]:
        """Analyze grammar and writing style"""
        try:
            grammar_data = {
                "pos_tags": [],
                "dependency_parse": [],
                "style_metrics": {}
            }
            
            if self.nlp_model:
                doc = self.nlp_model(text)
                
                # POS tagging
                for token in doc:
                    grammar_data["pos_tags"].append({
                        "text": token.text,
                        "pos": token.pos_,
                        "tag": token.tag_,
                        "lemma": token.lemma_
                    })
                
                # Dependency parsing
                for token in doc:
                    grammar_data["dependency_parse"].append({
                        "text": token.text,
                        "dep": token.dep_,
                        "head": token.head.text,
                        "children": [child.text for child in token.children]
                    })
            
            # Style metrics
            words = text.split()
            sentences = text.split('.')
            
            grammar_data["style_metrics"] = {
                "passive_voice_ratio": await self.detect_passive_voice(text),
                "sentence_variety": len(set(len(s.split()) for s in sentences if s.strip())),
                "word_variety": len(set(words)) / max(len(words), 1),
                "formality_score": await self.assess_formality(text)
            }
            
            return grammar_data
            
        except Exception as e:
            logger.error(f"Grammar analysis error: {e}")
            return {"pos_tags": [], "style_metrics": {}}
    
    async def summarize_text(self, text: str, max_length: int = 150) -> Dict[str, Any]:
        """Generate text summary"""
        try:
            if not self.text_summarizer or len(text) < 100:
                # Fallback to extractive summarization
                sentences = text.split('.')
                if len(sentences) <= 2:
                    return {"summary": text, "method": "original"}
                
                # Simple extractive summary - take first and last sentences
                summary = f"{sentences[0].strip()}. {sentences[-1].strip()}."
                return {"summary": summary, "method": "extractive"}
            
            # Use transformer model
            summary_result = self.text_summarizer(text, max_length=max_length, min_length=30)
            
            return {
                "summary": summary_result[0]["summary_text"],
                "method": "abstractive",
                "compression_ratio": len(summary_result[0]["summary_text"]) / len(text)
            }
            
        except Exception as e:
            logger.error(f"Text summarization error: {e}")
            return {"summary": text[:200] + "...", "method": "truncation"}
    
    async def answer_question(self, question: str, context: str) -> Dict[str, Any]:
        """Answer questions based on context"""
        try:
            if not self.question_answerer:
                return {"answer": "Question answering not available", "confidence": 0}
            
            result = self.question_answerer(question=question, context=context)
            
            return {
                "answer": result["answer"],
                "confidence": result["score"],
                "start": result["start"],
                "end": result["end"]
            }
            
        except Exception as e:
            logger.error(f"Question answering error: {e}")
            return {"answer": "Unable to answer question", "confidence": 0}
    
    async def generate_text(self, prompt: str, max_length: int = 100) -> Dict[str, Any]:
        """Generate text based on prompt"""
        try:
            if not self.text_generator:
                return {"generated_text": "Text generation not available"}
            
            result = self.text_generator(prompt, max_length=max_length, num_return_sequences=1)
            
            return {
                "generated_text": result[0]["generated_text"],
                "prompt": prompt,
                "model": "gpt2-medium"
            }
            
        except Exception as e:
            logger.error(f"Text generation error: {e}")
            return {"generated_text": "Text generation failed"}
    
    def aggregate_sentiment_scores(self, sentiment_results: Dict[str, Any]) -> Dict[str, Any]:
        """Aggregate sentiment scores from multiple models"""
        try:
            # Simple aggregation logic
            positive_score = 0
            negative_score = 0
            neutral_score = 0
            
            if "transformer" in sentiment_results:
                trans_result = sentiment_results["transformer"]
                if trans_result["label"] == "POSITIVE":
                    positive_score += trans_result["score"]
                elif trans_result["label"] == "NEGATIVE":
                    negative_score += trans_result["score"]
                else:
                    neutral_score += trans_result["score"]
            
            if "textblob" in sentiment_results:
                polarity = sentiment_results["textblob"]["polarity"]
                if polarity > 0.1:
                    positive_score += abs(polarity)
                elif polarity < -0.1:
                    negative_score += abs(polarity)
                else:
                    neutral_score += 0.5
            
            # Determine overall sentiment
            scores = {"positive": positive_score, "negative": negative_score, "neutral": neutral_score}
            overall_sentiment = max(scores, key=scores.get)
            confidence = scores[overall_sentiment] / sum(scores.values()) if sum(scores.values()) > 0 else 0.5
            
            return {
                "sentiment": overall_sentiment,
                "confidence": confidence,
                "scores": scores
            }
            
        except Exception as e:
            logger.error(f"Sentiment aggregation error: {e}")
            return {"sentiment": "neutral", "confidence": 0.5}
    
    async def extract_intent_entities(self, text: str, intent: str) -> List[Dict[str, Any]]:
        """Extract entities specific to the detected intent"""
        try:
            entities = []
            
            # Intent-specific entity extraction
            if intent == "booking":
                # Look for dates, times, services
                date_pattern = r'\b(?:today|tomorrow|next week|monday|tuesday|wednesday|thursday|friday|saturday|sunday|\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\b'
                time_pattern = r'\b\d{1,2}:\d{2}(?:\s?(?:AM|PM|am|pm))?\b'
                
                dates = re.findall(date_pattern, text, re.IGNORECASE)
                times = re.findall(time_pattern, text)
                
                for date in dates:
                    entities.append({"type": "date", "value": date})
                for time in times:
                    entities.append({"type": "time", "value": time})
            
            elif intent == "control":
                # Look for device names and actions
                device_pattern = r'\b(?:lights?|tv|television|music|volume|temperature|thermostat|fan|air conditioning)\b'
                action_pattern = r'\b(?:turn on|turn off|increase|decrease|set|adjust)\b'
                
                devices = re.findall(device_pattern, text, re.IGNORECASE)
                actions = re.findall(action_pattern, text, re.IGNORECASE)
                
                for device in devices:
                    entities.append({"type": "device", "value": device})
                for action in actions:
                    entities.append({"type": "action", "value": action})
            
            return entities
            
        except Exception as e:
            logger.error(f"Intent entity extraction error: {e}")
            return []
    
    async def analyze_context(self, text: str) -> Dict[str, Any]:
        """Analyze contextual information in text"""
        try:
            context = {
                "urgency": "normal",
                "formality": "neutral",
                "emotion_intensity": "moderate",
                "temporal_references": [],
                "spatial_references": []
            }
            
            # Urgency detection
            urgent_words = ["urgent", "emergency", "asap", "immediately", "now", "quickly", "hurry"]
            if any(word in text.lower() for word in urgent_words):
                context["urgency"] = "high"
            
            # Formality assessment
            formal_indicators = ["please", "thank you", "would you", "could you", "may i"]
            informal_indicators = ["hey", "yo", "gonna", "wanna", "yeah"]
            
            formal_count = sum(1 for indicator in formal_indicators if indicator in text.lower())
            informal_count = sum(1 for indicator in informal_indicators if indicator in text.lower())
            
            if formal_count > informal_count:
                context["formality"] = "formal"
            elif informal_count > formal_count:
                context["formality"] = "informal"
            
            return context
            
        except Exception as e:
            logger.error(f"Context analysis error: {e}")
            return {"urgency": "normal", "formality": "neutral"}
    
    async def detect_passive_voice(self, text: str) -> float:
        """Detect passive voice usage ratio"""
        try:
            if not self.nlp_model:
                return 0.0
            
            doc = self.nlp_model(text)
            passive_count = 0
            total_sentences = 0
            
            for sent in doc.sents:
                total_sentences += 1
                # Simple passive voice detection
                for token in sent:
                    if token.dep_ == "auxpass" or (token.dep_ == "nsubjpass"):
                        passive_count += 1
                        break
            
            return passive_count / max(total_sentences, 1)
            
        except Exception as e:
            logger.error(f"Passive voice detection error: {e}")
            return 0.0
    
    async def assess_formality(self, text: str) -> float:
        """Assess formality level of text (0-1 scale)"""
        try:
            formal_indicators = [
                "please", "thank you", "would", "could", "may", "shall",
                "furthermore", "however", "therefore", "consequently"
            ]
            
            informal_indicators = [
                "hey", "yo", "gonna", "wanna", "yeah", "nope", "ok", "cool"
            ]
            
            text_lower = text.lower()
            formal_count = sum(1 for word in formal_indicators if word in text_lower)
            informal_count = sum(1 for word in informal_indicators if word in text_lower)
            
            total_indicators = formal_count + informal_count
            if total_indicators == 0:
                return 0.5  # Neutral
            
            return formal_count / total_indicators
            
        except Exception as e:
            logger.error(f"Formality assessment error: {e}")
            return 0.5
    
    async def cleanup(self):
        """Cleanup NLP service"""
        logger.info("Advanced NLP Service cleanup completed")
