"""Analytics and monitoring router"""

from fastapi import APIRouter, Depends
from ..core.database import User
from ..core.security import get_current_active_user

router = APIRouter()

@router.get("/usage")
async def get_usage_stats(current_user: User = Depends(get_current_active_user)):
    return {"commands_executed": 0, "voice_interactions": 0, "automations_run": 0}

@router.get("/performance")
async def get_performance_metrics():
    return {"cpu_usage": 25.5, "memory_usage": 45.2, "response_time": 150}
