import React, { useEffect, useState, useRef } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';

// Components
import Dashboard from './components/Dashboard';
import VoiceInterface from './components/VoiceInterface';
import DeviceControl from './components/DeviceControl';
import SmartHome from './components/SmartHome';
import Settings from './components/Settings';
import Login from './components/Login';

// Hooks and utilities
import { useAuthStore } from './store/authStore';
import { useJarvisStore } from './store/jarvisStore';

import './App.css';

const BACKEND_API_URL = 'http://localhost:8000';

// Create theme
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00d4ff',
    },
    secondary: {
      main: '#ff6b35',
    },
    background: {
      default: '#0a0a0a',
      paper: '#1a1a1a',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 300,
    },
  },
});

// Create query client
const queryClient = new QueryClient();

function App() {
  const { isAuthenticated } = useAuthStore();
  const { isInitialized, initialize } = useJarvisStore();

  useEffect(() => {
    if (!isInitialized) {
      initialize();
    }
  }, [isInitialized, initialize]);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={darkTheme}>
        <CssBaseline />
        <Router>
          <div className="App">
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#1a1a1a',
                  color: '#fff',
                  border: '1px solid #00d4ff',
                },
              }}
            />

            {!isAuthenticated ? (
              <Login />
            ) : (
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/voice" element={<VoiceInterface />} />
                <Route path="/devices" element={<DeviceControl />} />
                <Route path="/smart-home" element={<SmartHome />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            )}
          </div>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
