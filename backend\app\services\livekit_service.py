"""
LiveKit Service for real-time audio/video communication
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import jwt
from livekit import api
from loguru import logger

from ..core.config import settings

class LiveKitService:
    """LiveKit service for real-time communication"""
    
    def __init__(self):
        self.api_key = settings.LIVEKIT_API_KEY
        self.api_secret = settings.LIVEKIT_API_SECRET
        self.url = settings.LIVEKIT_URL
        self.room_service = None
        self.active_rooms = {}
        
    async def initialize(self):
        """Initialize LiveKit service"""
        if not self.api_key or not self.api_secret:
            logger.warning("LiveKit credentials not provided")
            return
        
        try:
            self.room_service = api.RoomService(
                self.url,
                self.api_key,
                self.api_secret
            )
            logger.info("LiveKit service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LiveKit service: {e}")
            raise
    
    def generate_access_token(
        self,
        room_name: str,
        participant_name: str,
        permissions: Optional[Dict[str, bool]] = None
    ) -> str:
        """Generate access token for LiveKit room"""
        try:
            if not permissions:
                permissions = {
                    "canPublish": True,
                    "canSubscribe": True,
                    "canPublishData": True
                }
            
            # Create token
            token = api.AccessToken(self.api_key, self.api_secret)
            token.with_identity(participant_name)
            token.with_name(participant_name)
            token.with_grants(api.VideoGrants(
                room_join=True,
                room=room_name,
                can_publish=permissions.get("canPublish", True),
                can_subscribe=permissions.get("canSubscribe", True),
                can_publish_data=permissions.get("canPublishData", True)
            ))
            
            # Set expiration (24 hours)
            token.with_ttl(timedelta(hours=24))
            
            return token.to_jwt()
            
        except Exception as e:
            logger.error(f"Error generating LiveKit token: {e}")
            raise
    
    async def create_room(
        self,
        room_name: str,
        max_participants: int = 10,
        empty_timeout: int = 300,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a new LiveKit room"""
        try:
            if not self.room_service:
                await self.initialize()
            
            room_options = api.CreateRoomRequest(
                name=room_name,
                empty_timeout=empty_timeout,
                max_participants=max_participants,
                metadata=json.dumps(metadata or {})
            )
            
            room = await self.room_service.create_room(room_options)
            
            self.active_rooms[room_name] = {
                "created_at": datetime.utcnow(),
                "participants": [],
                "metadata": metadata or {}
            }
            
            logger.info(f"Created LiveKit room: {room_name}")
            
            return {
                "room_name": room.name,
                "creation_time": room.creation_time,
                "max_participants": room.max_participants,
                "metadata": metadata
            }
            
        except Exception as e:
            logger.error(f"Error creating LiveKit room: {e}")
            raise
    
    async def get_room_info(self, room_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a room"""
        try:
            if not self.room_service:
                await self.initialize()
            
            rooms = await self.room_service.list_rooms(api.ListRoomsRequest())
            
            for room in rooms:
                if room.name == room_name:
                    return {
                        "name": room.name,
                        "creation_time": room.creation_time,
                        "num_participants": room.num_participants,
                        "max_participants": room.max_participants,
                        "metadata": json.loads(room.metadata) if room.metadata else {}
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting room info: {e}")
            return None
    
    async def list_participants(self, room_name: str) -> List[Dict[str, Any]]:
        """List participants in a room"""
        try:
            if not self.room_service:
                await self.initialize()
            
            participants = await self.room_service.list_participants(
                api.ListParticipantsRequest(room=room_name)
            )
            
            result = []
            for participant in participants:
                result.append({
                    "identity": participant.identity,
                    "name": participant.name,
                    "state": participant.state,
                    "joined_at": participant.joined_at,
                    "metadata": json.loads(participant.metadata) if participant.metadata else {}
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error listing participants: {e}")
            return []
    
    async def remove_participant(self, room_name: str, participant_identity: str) -> bool:
        """Remove a participant from a room"""
        try:
            if not self.room_service:
                await self.initialize()
            
            await self.room_service.remove_participant(
                api.RoomParticipantIdentity(
                    room=room_name,
                    identity=participant_identity
                )
            )
            
            logger.info(f"Removed participant {participant_identity} from room {room_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing participant: {e}")
            return False
    
    async def delete_room(self, room_name: str) -> bool:
        """Delete a room"""
        try:
            if not self.room_service:
                await self.initialize()
            
            await self.room_service.delete_room(
                api.DeleteRoomRequest(room=room_name)
            )
            
            if room_name in self.active_rooms:
                del self.active_rooms[room_name]
            
            logger.info(f"Deleted LiveKit room: {room_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting room: {e}")
            return False
    
    async def send_data(
        self,
        room_name: str,
        data: Dict[str, Any],
        participant_identities: Optional[List[str]] = None
    ) -> bool:
        """Send data to participants in a room"""
        try:
            if not self.room_service:
                await self.initialize()
            
            data_packet = api.SendDataRequest(
                room=room_name,
                data=json.dumps(data).encode(),
                destination_identities=participant_identities or []
            )
            
            await self.room_service.send_data(data_packet)
            return True
            
        except Exception as e:
            logger.error(f"Error sending data: {e}")
            return False
    
    async def create_jarvis_room(self, user_id: str) -> Dict[str, Any]:
        """Create a dedicated JARVIS room for a user"""
        room_name = f"jarvis-{user_id}"
        
        metadata = {
            "type": "jarvis_session",
            "user_id": user_id,
            "created_at": datetime.utcnow().isoformat(),
            "features": [
                "voice_recognition",
                "voice_synthesis",
                "real_time_ai",
                "device_control"
            ]
        }
        
        room_info = await self.create_room(
            room_name=room_name,
            max_participants=2,  # User + JARVIS
            empty_timeout=1800,  # 30 minutes
            metadata=metadata
        )
        
        # Generate tokens
        user_token = self.generate_access_token(
            room_name=room_name,
            participant_name=f"user-{user_id}",
            permissions={
                "canPublish": True,
                "canSubscribe": True,
                "canPublishData": True
            }
        )
        
        jarvis_token = self.generate_access_token(
            room_name=room_name,
            participant_name="jarvis-ai",
            permissions={
                "canPublish": True,
                "canSubscribe": True,
                "canPublishData": True
            }
        )
        
        return {
            "room_info": room_info,
            "user_token": user_token,
            "jarvis_token": jarvis_token,
            "room_url": f"{self.url}?token={user_token}"
        }
    
    async def handle_voice_command(
        self,
        room_name: str,
        audio_data: bytes,
        user_id: str
    ) -> Dict[str, Any]:
        """Handle voice command in a LiveKit room"""
        try:
            # This would integrate with voice recognition service
            # For now, return a placeholder response
            
            response_data = {
                "type": "voice_response",
                "message": "Voice command processed",
                "timestamp": datetime.utcnow().isoformat(),
                "user_id": user_id
            }
            
            # Send response back to room
            await self.send_data(room_name, response_data)
            
            return response_data
            
        except Exception as e:
            logger.error(f"Error handling voice command: {e}")
            return {"error": str(e)}
    
    async def cleanup(self):
        """Cleanup LiveKit service"""
        try:
            # Clean up active rooms
            for room_name in list(self.active_rooms.keys()):
                await self.delete_room(room_name)
            
            logger.info("LiveKit service cleanup completed")
        except Exception as e:
            logger.error(f"Error during LiveKit cleanup: {e}")
