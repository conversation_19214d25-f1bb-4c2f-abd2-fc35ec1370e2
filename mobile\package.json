{"name": "jarvis-mobile", "version": "1.0.0", "description": "JARVIS Ultra-Advanced AI Assistant Mobile App", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "9.3.10", "@react-native-voice/voice": "^3.2.4", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "expo": "~49.0.15", "expo-av": "~13.4.1", "expo-camera": "~13.4.4", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-file-system": "~15.4.5", "expo-font": "~11.4.0", "expo-image-picker": "~14.3.2", "expo-linear-gradient": "~12.3.0", "expo-location": "~16.1.0", "expo-notifications": "~0.20.1", "expo-permissions": "~14.2.1", "expo-sensors": "~12.4.0", "expo-speech": "~11.3.0", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-animatable": "^1.3.3", "react-native-gesture-handler": "~2.12.0", "react-native-paper": "^5.10.6", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-vector-icons": "^10.0.0", "react-native-webview": "13.2.2", "socket.io-client": "^4.7.2", "axios": "^1.5.0", "react-query": "^3.39.3", "zustand": "^4.4.1", "react-hook-form": "^7.45.4", "react-native-super-grid": "^4.9.6", "react-native-modal": "^13.0.1", "react-native-chart-kit": "^6.12.0", "react-native-progress": "^5.0.1", "lottie-react-native": "6.0.1", "react-native-blur": "^4.3.2", "react-native-haptic-feedback": "^2.2.0", "react-native-biometrics": "^3.0.1", "react-native-keychain": "^8.1.3", "react-native-background-job": "^0.2.9", "react-native-push-notification": "^8.1.1", "react-native-device-info": "^10.11.0", "react-native-orientation-locker": "^1.5.0", "react-native-share": "^9.4.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-image-crop-picker": "^0.40.2", "react-native-video": "^5.2.1", "react-native-sound": "^0.11.2", "react-native-fs": "^2.20.0", "react-native-zip-archive": "^6.0.8", "react-native-contacts": "^7.0.8", "react-native-calendar-events": "^2.2.0", "react-native-maps": "1.7.1", "react-native-geolocation-service": "^5.3.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "eslint-config-expo": "^7.0.0", "jest": "^29.2.1", "typescript": "^5.1.3"}, "keywords": ["jarvis", "ai", "assistant", "mobile", "react-native", "expo", "voice", "computer-vision", "automation"], "author": "JARVIS Team", "license": "MIT", "private": true}