apiVersion: apps/v1
kind: Deployment
metadata:
  name: jarvis-frontend
  namespace: jarvis
  labels:
    app: jarvis-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: jarvis-frontend
  template:
    metadata:
      labels:
        app: jarvis-frontend
    spec:
      containers:
      - name: jarvis-frontend
        image: jarvis/frontend:latest
        ports:
        - containerPort: 3000
        env:
        - name: REACT_APP_API_URL
          value: "https://api.jarvis.ai"
        - name: REACT_APP_WS_URL
          value: "wss://api.jarvis.ai/ws"
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: jarvis-frontend-service
  namespace: jarvis
spec:
  selector:
    app: jarvis-frontend
  ports:
  - protocol: TCP
    port: 3000
    targetPort: 3000
  type: ClusterIP
