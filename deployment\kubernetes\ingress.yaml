apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jarvis-ingress
  namespace: jarvis
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/websocket-services: "jarvis-backend-service"
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
spec:
  tls:
  - hosts:
    - jarvis.ai
    - api.jarvis.ai
    - app.jarvis.ai
    secretName: jarvis-tls
  rules:
  - host: jarvis.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: jarvis-frontend-service
            port:
              number: 3000
  - host: app.jarvis.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: jarvis-frontend-service
            port:
              number: 3000
  - host: api.jarvis.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: jarvis-backend-service
            port:
              number: 8000
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
