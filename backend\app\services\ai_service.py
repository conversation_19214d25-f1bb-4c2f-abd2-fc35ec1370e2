"""
Advanced AI Service for JARVIS
Handles natural language processing, conversation, and AI-powered features
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import openai
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.memory import ConversationBufferWindowMemory
from loguru import logger

from ..core.config import settings
from ..core.redis_client import redis_client

class AIService:
    """Advanced AI service with conversation management and context awareness"""
    
    def __init__(self):
        self.openai_client = None
        self.chat_model = None
        self.memory = {}
        self.system_prompt = self._get_system_prompt()
        
    async def initialize(self):
        """Initialize AI service"""
        if not settings.OPENAI_API_KEY:
            logger.warning("OpenAI API key not provided")
            return
        
        try:
            openai.api_key = settings.OPENAI_API_KEY
            self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
            
            self.chat_model = ChatOpenAI(
                model=settings.OPENAI_MODEL,
                temperature=0.7,
                openai_api_key=settings.OPENAI_API_KEY
            )
            
            logger.info("AI Service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize AI service: {e}")
            raise
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for JARVIS"""
        return """You are JARVIS, an advanced AI assistant with comprehensive capabilities. You can:

1. **Voice & Communication**: Process voice commands, generate speech, handle conversations
2. **Device Control**: Control mobile devices, computers, smart home devices, IoT systems
3. **Automation**: Create and execute complex workflows and automations
4. **Information**: Search the web, gather real-time information, analyze data
5. **Vision**: Process images, recognize objects, analyze visual content
6. **Smart Home**: Control lights, temperature, security systems, appliances
7. **Productivity**: Manage calendars, send emails/messages, create documents
8. **Entertainment**: Play music, control media, recommend content
9. **Security**: Monitor systems, detect anomalies, manage access
10. **Learning**: Adapt to user preferences, learn from interactions

You should be helpful, proactive, and anticipate user needs. Always provide clear, actionable responses and suggest relevant follow-up actions. When handling commands, break them down into specific actions and execute them systematically.

Current capabilities include:
- Real-time voice processing
- Computer vision and image analysis
- Web scraping and information retrieval
- Device automation and control
- Smart home integration
- Workflow automation
- File processing and management
- Communication (email, SMS, social media)
- Calendar and scheduling
- Weather and location services
- Entertainment control
- Security monitoring

Respond naturally and conversationally while being precise and helpful."""

    async def process_command(self, command: str, user: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> str:
        """Process a user command with AI"""
        try:
            user_id = user.get("id", "unknown")
            
            # Get conversation history
            conversation_history = await self._get_conversation_history(user_id)
            
            # Analyze command intent
            intent = await self._analyze_intent(command)
            
            # Process based on intent
            if intent["category"] == "device_control":
                response = await self._handle_device_control(command, intent, user)
            elif intent["category"] == "smart_home":
                response = await self._handle_smart_home(command, intent, user)
            elif intent["category"] == "information":
                response = await self._handle_information_request(command, intent, user)
            elif intent["category"] == "automation":
                response = await self._handle_automation(command, intent, user)
            elif intent["category"] == "communication":
                response = await self._handle_communication(command, intent, user)
            elif intent["category"] == "entertainment":
                response = await self._handle_entertainment(command, intent, user)
            else:
                response = await self._handle_general_conversation(command, conversation_history, user)
            
            # Update conversation history
            await self._update_conversation_history(user_id, command, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing command: {e}")
            return "I encountered an error processing your request. Please try again."
    
    async def _analyze_intent(self, command: str) -> Dict[str, Any]:
        """Analyze command intent using AI"""
        try:
            prompt = f"""Analyze the following command and categorize it:

Command: "{command}"

Categories:
- device_control: Control phone, computer, or other devices
- smart_home: Control lights, temperature, appliances, security
- information: Search, weather, news, facts, calculations
- automation: Create workflows, schedules, reminders
- communication: Send messages, emails, make calls
- entertainment: Play music, videos, games, recommendations
- file_management: Handle files, documents, photos
- general: Conversation, questions, general assistance

Respond with JSON:
{{
    "category": "category_name",
    "confidence": 0.95,
    "entities": ["entity1", "entity2"],
    "action": "specific_action",
    "parameters": {{"param1": "value1"}}
}}"""

            response = await self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            result = json.loads(response.choices[0].message.content)
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing intent: {e}")
            return {
                "category": "general",
                "confidence": 0.5,
                "entities": [],
                "action": "conversation",
                "parameters": {}
            }
    
    async def _handle_device_control(self, command: str, intent: Dict[str, Any], user: Dict[str, Any]) -> str:
        """Handle device control commands"""
        # This would integrate with device control APIs
        action = intent.get("action", "")
        
        if "volume" in command.lower():
            return "I'll adjust the volume for you. Device control executed."
        elif "brightness" in command.lower():
            return "I'll adjust the screen brightness. Device control executed."
        elif "wifi" in command.lower():
            return "I'll manage the WiFi settings. Device control executed."
        else:
            return f"I'll execute the device control command: {command}"
    
    async def _handle_smart_home(self, command: str, intent: Dict[str, Any], user: Dict[str, Any]) -> str:
        """Handle smart home commands"""
        # This would integrate with smart home APIs
        if "lights" in command.lower():
            return "I'll control the lights for you. Smart home command executed."
        elif "temperature" in command.lower():
            return "I'll adjust the temperature. Smart home command executed."
        elif "security" in command.lower():
            return "I'll manage the security system. Smart home command executed."
        else:
            return f"I'll execute the smart home command: {command}"
    
    async def _handle_information_request(self, command: str, intent: Dict[str, Any], user: Dict[str, Any]) -> str:
        """Handle information requests"""
        # This would integrate with web search and information APIs
        if "weather" in command.lower():
            return "Let me get the current weather information for you."
        elif "news" in command.lower():
            return "I'll fetch the latest news for you."
        else:
            return f"I'll search for information about: {command}"
    
    async def _handle_automation(self, command: str, intent: Dict[str, Any], user: Dict[str, Any]) -> str:
        """Handle automation commands"""
        # This would integrate with workflow automation
        return f"I'll create an automation for: {command}"
    
    async def _handle_communication(self, command: str, intent: Dict[str, Any], user: Dict[str, Any]) -> str:
        """Handle communication commands"""
        # This would integrate with communication APIs
        if "email" in command.lower():
            return "I'll help you with email communication."
        elif "message" in command.lower() or "text" in command.lower():
            return "I'll send a message for you."
        else:
            return f"I'll handle the communication request: {command}"
    
    async def _handle_entertainment(self, command: str, intent: Dict[str, Any], user: Dict[str, Any]) -> str:
        """Handle entertainment commands"""
        # This would integrate with entertainment APIs
        if "music" in command.lower():
            return "I'll play some music for you."
        elif "video" in command.lower():
            return "I'll find a video for you."
        else:
            return f"I'll handle the entertainment request: {command}"
    
    async def _handle_general_conversation(self, command: str, history: List[Dict[str, Any]], user: Dict[str, Any]) -> str:
        """Handle general conversation"""
        try:
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history
            for msg in history[-10:]:  # Last 10 messages
                messages.append({"role": "user", "content": msg["message"]})
                messages.append({"role": "assistant", "content": msg["response"]})
            
            # Add current message
            messages.append({"role": "user", "content": command})
            
            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                temperature=0.7,
                max_tokens=500
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error in general conversation: {e}")
            return "I'm here to help! Could you please rephrase your request?"
    
    async def _get_conversation_history(self, user_id: str) -> List[Dict[str, Any]]:
        """Get conversation history from cache"""
        try:
            history = await redis_client.get(f"conversation_history:{user_id}", [])
            return history if isinstance(history, list) else []
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    async def _update_conversation_history(self, user_id: str, message: str, response: str):
        """Update conversation history in cache"""
        try:
            history = await self._get_conversation_history(user_id)
            
            # Add new message
            history.append({
                "message": message,
                "response": response,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # Keep only last 50 messages
            if len(history) > 50:
                history = history[-50:]
            
            # Cache for 24 hours
            await redis_client.set(f"conversation_history:{user_id}", history, expire=86400)
            
        except Exception as e:
            logger.error(f"Error updating conversation history: {e}")
    
    async def cleanup(self):
        """Cleanup AI service"""
        logger.info("AI Service cleanup completed")
